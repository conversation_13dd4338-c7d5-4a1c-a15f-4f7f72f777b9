/* ================================================== */


/* AutoDetail
/* created by designesia

@charset "utf-8";

Table of content:

* body
* header
  * subheader
* navigation
* content
* section
* footer
* sidebar
* contact
* elements
  * border
  * breadcrumb
  * button and link
  * columns
  * counter
  * divider
  * dropcap
  * heading
  * list
  * map
  * page heading
  * pagination
  * preloader
  * progress bar
  * social icons
  * spacer
  * tabs
  * testimonial
* plugins
* media query
*/

/* ================================================== */

@import url('https://fonts.googleapis.com/css2?family=Oxanium:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* ================================================== */


/* import fonts icon */


/* ================================================== */

@import url("../fonts/fontawesome4/css/font-awesome.css");
@import url("../fonts/fontawesome6/css/fontawesome.css");
@import url("../fonts/fontawesome6/css/brands.css");
@import url("../fonts/fontawesome6/css/solid.css");
@import url("../fonts/elegant_font/HTML_CSS/style.css");
@import url("../fonts/et-line-font/style.css");
@import url("../fonts/icofont/icofont.min.css");

* {
  /* body font */
  --body-font: "Manrope", Helvetica, Arial, sans-serif;
  --body-font-size: 16px;
  --body-font-color: #8a8a8a;
  --body-font-color-dark: rgba(255, 255, 255, .65);
  --body-font-weight: 400;
  /* heading font */
  --heading-font: "Oxanium", Helvetica, Arial, sans-serif;  
  --heading-font-weight: 500;
  --heading-font-color: #002060;
  --heading-text-transform: none;
  /* h1 */
  --h1-font-size: 60px;
  --h1-font-weight: var(--heading-font-weight);
  --h1-letter-spacing: -0.02em;
  --h1-line-height: 1.15em;
  --h1-margin-bottom: 20px;
  /* h2 */
  --h2-font: var(--heading-font);
  --h2-font-size: 48px;
  --h2-font-weight: var(--heading-font-weight);
  --h2-letter-spacing: -0.015em;
  --h2-line-height: 1.2em;
  --h2-margin-bottom: 15px;
  /* h3 */
  --h3-font-size: 26px;
  --h3-font-weight: 600;
  --h3-letter-spacing: 0;
  --h3-line-height: 1.5em;
  --h3-margin-bottom: 10px;
  /* h4 */
  --h4-font-size: 20px;
  --h4-font-weight: 600;
  --h4-letter-spacing: 0;
  --h4-line-height: 1.6em;
  --h4-margin-bottom: 10px;
  /* h5 */
  --h5-font-size: 18px;
  --h5-font-weight: 600;
  --h5-letter-spacing: 0;
  --h5-line-height: 1.6em;
  --h5-margin-bottom: 10px;
  /* h6 */
  --h6-font-size: 16px;
  --h6-font-weight: 600;
  --h6-letter-spacing: 0;
  --h6-line-height: 1.6em;
  --h6-margin-bottom: 10px;
  /* mainmenu */
  --mainmenu-font: var(--body-font);
  --mainmenu-font-size: 16px;
  --mainmenu-font-weight: 500;
  --mainmenu-letter-spacing: 0;
  --mainmenu-text-transform: none;
  /* header logo */
  --logo-width: 240px;
  /* misc */
  --border-default: solid 1px rgba(30, 30, 30, 1);
  --bg-color-even: #E8E8E8;
  --bg-color-odd: #F4F4F4;
  --bg-light: #F8F9FA;
  --bg-dark-1: #1B1663;
  --bg-dark-2: #120d4f;
  --bg-dark-3: #1e1e1e;  
  --bg-dark-1-rgb: 27, 22, 99;
  --bg-grey: #eeeeee;
  --bg-gradient-1: 0deg, rgba(var(--primary-color-rgb), .1) 0%, rgba(var(--primary-color-rgb), .2) 100%;
  --swiper-theme-color: var(--primary-color);
  --rounded-1: 10px;
  --border-color:#bbbbbb;
  --container-max-width:1240px;
  /* button */
  --btn-color: #ffffff;
  --btn-font-family: var(--body-font);
  --btn-font-size: 15px;
  --btn-font-weight: 600;
  --btn-letter-spacing: 0;
  --btn-padding: 6px 20px;
  --btn-rounded: 6px;
  --btn-text-decoration: none;
  --btn-text-transform: none;
}

/* base background color */
.bg-color{background: var(--primary-color) !important}
.bg-light{background: var(--bg-light) !important}
.bg-dark,.bg-dark-1{background-color: var(--bg-dark-1) !important}
.bg-dark-2{background-color: var(--bg-dark-2) !important}
.bg-dark-3{background-color: var(--bg-dark-3) !important}
.bg-grey{background-color: var(--bg-grey) !important}
.bg-gradient-1{  background: -moz-linear-gradient(var(--bg-gradient-1)) !important;  background: -webkit-linear-gradient(var(--bg-gradient-1)) !important;background: linear-gradient(var(--bg-gradient-1)) !important;
}
/* base color */
.id-color{color:var(--primary-color)}
.rounded-1{
  border-radius: var(--rounded-1) !important;
  -moz-border-radius: var(--rounded-1) !important;
  -webkit-border-radius: var(--rounded-1) !important;
}
.alt-font-1{font-family:var(--alt-font-1);}

/* ================================================== */


/* body */


/* ================================================== */

html {
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

body {
  overflow-x: none;
  font-family: var(--body-font);
  font-size: var(--body-font-size);
  font-weight: var(--body-font-weight);;
  color: var(--body-font-color);
  padding: 0;
  line-height: 1.9em;
  word-spacing: 0px;
}

body.dark-scheme {
  color: var(--body-font-color-dark);
  background: var(--bg-dark-1);
}

a,
a:hover {
  color:var(--body-font-color);
  text-decoration: none;
  outline: none;
}

.dark-scheme a,
.dark-scheme a:hover,
.text-light a,
.text-light a:hover {
  color:var(--body-font-color-dark);
  text-decoration: none;
  outline: none;
}


/* ================================================== */


/*  header  */


/* ================================================== */

header {
  width: 100%;
  position: absolute;
  left: 0;
  z-index: 1001;
  background: var(--bg-dark-1);
  margin: 0;
}

header.header-s1 {
  top: 0;
}

header.header-bg {
  background: rgba(0, 0, 0, 0.5);
}

header.transparent {
  background: none;
}

body:not(.side-layout) header:not(.smaller):not(.header-mobile).header-light.transparent {
  background: rgba(255, 255, 255, 0.0);
}

header.autoshow {
  top: -120px;
  height: 70px !important;
}

header.autoshow.scrollOn,
header.autoshow.scrollOff {
  -o-transition: .5s;
  -ms-transition: .5s;
  -moz-transition: .5s;
  -webkit-transition: .5s;
  transition: .5s;
  outline: none;
}

header.autoshow.scrollOn {
  top: 0;
}

header.autoshow.scrollOff {
  opacity: ;
  top: -120px;
}

header div#logo {
  color: white;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

header .logo-2 {
  display: none;
}

header .logo-scroll {
  display: none;
}

header .logo,
header .logo-2 {
  font-family: var(--body-font);
  color: #fff;
  font-size: 40px;
  font-weight: 300;
}

header .logo-2 {
  color: #333;
}

header.header-full nav {
  float: left;
}

header nav a {
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}

header nav a:hover {
  color: #555;
}

header.smaller #mainmenu ul ul {
  top: 0px;
}

header.smaller {
  position: fixed;
  top: 0px;
  background: rgba(var(--bg-dark-1-rgb), .65);
  -webkit-backdrop-filter: blur(10px);
  -o-backdrop-filter: blur(10px);
  -moz-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: none;*/
}

header.header-light.smaller{
  background: rgba(255, 255, 255, 1.0);
}

.scroll-down {
    margin-top: -90px;
}

.scroll-up {
    margin-top: 0;
}

header.header-center.scroll-down{
  margin-top: -175px;
}

header.header-center #logo-center{
  display: inline-block;
  padding-top: 40px;
}

header.header-center #mainmenu{
  display: inline-block;
}

header.header-center{
  text-align: center;
}

header.header-center #logo{
  display: none;
}

header.smaller div#logo,
header.s2 div#logo {}

header.smaller .logo-init {
  display: none;
}

header.smaller #topbar {
  margin-top:-42px;
  overflow: hidden;
}

header.smaller #topbar.topbar-dark {
  background: rgba(34, 34, 34, .05);
}

header.smaller #topbar.topbar-dark a {
  color: #333333;
}

header .logo-mobile {
  display: none;
}


header.smaller.scroll-light {
  background: #ffffff;
}

header.scroll-light .logo-scroll{
  display: none;
}

header.smaller.scroll-light .logo-main{
  display: none;
}

header.smaller.scroll-light .logo-scroll{
  display: block;
}

header.smaller.scroll-dark {
  background: var(--bg-color-even);
}

header.header-light .h-phone,
header.smaller.scroll-light .h-phone {
  color: #202020;
}

header:not(.header-light).smaller.scroll-light div#logo .logo {
  display: none;
}

header.smaller.scroll-light div#logo .logo-2 {
  display: inline-block;
}

header.smaller.scroll-light div#logo .logo-scroll {
  display: inline-block;
}

header.smaller.header-light div#logo .logo {
  display: none;
}

header.smaller.scroll-light .social-icons i {
  color: var(--heading-font-color);
}

header.smaller.scroll-light #mainmenu li a,
header.smaller.scroll-light #mainmenu li.has-child:after {
  color: var(--heading-font-color);
}

.logo-smaller div#logo {
  width: 150px;
  height: 30px;
  line-height: 65px;
  font-size: 30px;
}

.logo-smaller div#logo img {
  font-size: 30px;
}

div#logo img{
  max-width: var(--logo-width);
}

.logo-footer{
  max-width: var(--logo-width);
}

header.smaller div#logo .logo-1 {
  display: none;
}

header.smaller div#logo .logo-2 {
  display: inline-block;
}

.logo-small {
  margin-bottom: 20px;
}

header.header-full {
  padding-left: 30px;
  padding-right: 30px;
}

header .header-row {
  text-align: center;
}

header .header-col {
  position: relative;
  height: 100%;
}

/* left col header */
header .header-col.left {
  float: left;
  padding: 0 30px 0 0;
  /* border-right:solid 1px rgba(255,255,255,.2); */
}

/* middle col header */
header .header-col.mid {
  float: right;
}

header .header-col.mid .social-icons {
  float: right;
  /* border-left:solid 1px rgba(255,255,255,.2); */
  padding: 0 30px 0 30px;
}


.header-col-right {
  text-align: right;
}
/* subheader */

#subheader {
  overflow: hidden;
  position: relative;
  padding: 160px 0 120px 0;
}

#subheader h1 {
  font-size: 48px;
  margin-bottom: 0;
  text-transform: uppercase;
}

#subheader.dark {
  background-color: #1A1C26;
}

#subheader.dark h1 {
  color: #fff;
}

.de-overlay{
    position: absolute;
    width: 100%;
    height: 100%;
    top:0;
    left: 0;
    background: rgba(var(--bg-dark-1-rgb), .35);
  }

.de-overlay-gradient{
  position: absolute;
  width: 100%;
  height: 100%;
  top:0;
  left: 0;
  background: linear-gradient(45deg, rgba(var(--primary-color-rgb),.8), rgba(var(--primary-color-rgb),.8));
}


.de-overlay-gradient-2{
  position: absolute;
  width: 100%;
  height: 100%;
  top:0;
  left: 0;
  background: linear-gradient(45deg, rgba(var(--primary-color-rgb),.8) 0%, rgba(var(--primary-color-rgb),0) 100%);
}

.crumb-wrapper{
  position: absolute;
  padding: 0 20px;
  padding-bottom: 5px;
  bottom: 0;
  z-index: 1;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, .1);
}

.crumb {
  font-size: 13px;
}


.crumb {
  display: inline-block;
  padding: 3px 10px;
  margin-top: 20px;
  line-height: 1.2em;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
}

.text-light .crumb a{
  color:rgba(255, 255, 255, 1.0);
}


#subheader.text-light .crumb {
  color: #fff;
}

#subheader .small-border-deco {
  display: block;
  margin: 0 auto;
  margin-top: 20px;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, .1);
}

#subheader .small-border-deco span {
  display: block;
  margin: 0 auto;
  height: 2px;
  width: 100px;
}

#subheader.dark h2,
#subheader.dark h4 {
  color: #fff;
}

#subheader.dark h2 {
  font-size: 48px;
}

#subheader.s2 {
  text-align: left;
  padding: 120px 0 20px;
  border: solid 1px #ddd;
  border-left: none;
  border-right: none;
}

.rtl #subheader.s2 {
  text-align: right;
}


#subheader.s2 h1,
#subheader.s2 .crumb {
  margin: 0;
  padding: 0;
}

#subheader.s2 .crumb {
  float: right;
  margin-top: 10px;
}

.rtl #subheader.s2 .crumb {
  float: left;
}


/* ================================================== */


/* navigation */


/* ================================================== */

#mainmenu {
  font-size: 13px;
  margin: 0 auto;
  padding: 0;
  float: none;
  counter-reset: my-awesome-counter;
}

#mainmenu ul {
  margin: 0px 0px;
  padding: 0px;
  height: 30px;
  background: #ffffff;
  color: var(--body-font-color);
}

#mainmenu ul:not(.mega) {
  border: solid 1px #dddddd;
}

.dark-scheme #mainmenu ul {
  background: var(--bg-dark-1);
  border: solid 1px rgba(255, 255, 255, .1);
}

#mainmenu li {
  margin: 0px 0px;
  padding: 0px 0px;
  float: left;
  display: inline;
  list-style: none;
  position: relative;
}

#mainmenu>li {
  margin-right: 30px;
}

#mainmenu>li:last-child{
  margin-right: 0;
}

#mainmenu>li {
  counter-increment: my-awesome-counter;
}

#mainmenu li a.new:before{
  content:"NEW";
  font-size: 12px;
  padding: 1px 4px;
  border-radius:3px;
  -moz-border-radius:3px;
  -webkit-border-radius:3px;
  margin-right: 5px;
  color:#ffffff;
  background: var(--primary-color);
}
/*
#mainmenu>li::before {
  content: counter(my-awesome-counter) ' —';
  right: 8px;
  top: 5px;
  color:var(--primary-color);
  margin-right: 5px;
  font-weight: 500;
  font-family: var(--heading-font);
}
*/

header.smaller #mainmenu>li::before{
  transform: scale(0);
}

#mainmenu>li.has-child:after {
  font-family: FontAwesome;
  content: "\f078";
  padding-left: 5px;
  font-size: 8px;
  position: relative;
  top: -2px;
  color:rgba(255, 255, 255, .75);
}

.rtl #mainmenu li.has-child:after {
  padding-left: 15px;
}

.header-light #mainmenu li.has-child:after {
  color: rgba(0, 0, 0, .5);
}

#mainmenu >li>a {  
  padding-right: 6px;
  font-family: var(--mainmenu-font);
  font-size: var(--mainmenu-font-size);
  font-weight: var(--mainmenu-font-weight);
  text-transform: var(--mainmenu-text-transform);
  letter-spacing: var(--mainmenu-letter-spacing);
}

#mainmenu li.has-child>a {
  padding-right: 0px;
}

#mainmenu a {
  position: relative;
  display: inline-block;
  padding-top: 25px;
  padding-bottom: 25px;
  text-decoration: none;
  color: #ffffff;
  text-align: center;
  outline: none;
}

#mainmenu p {
  line-height: 1.8em;
  margin-bottom: 0;
}

header.header-light #mainmenu a {
  color: var(--heading-font-color);
}

#mainmenu a span:not(.badge) {
  position: relative;
  width: 0%;
  color: #ffffff;
  display: block;
  font-size: 10px;
  text-transform: uppercase;
  line-height: 14px;
  border-bottom: solid 2px #ffffff;
}

#mainmenu li:hover a span:not(.badge) {
  width: 100%;
}

#mainmenu li li a span:not(.badge) {
  float: right;
  margin-top: 5px;
}

#mainmenu a.active span:not(.badge) {
  width: 100%;
  margin-left: 0%;
}

.header-light {
  background: #ffffff;
}

.header-light #mainmenu>li>a {
  font-weight: bold;
  color: var(--heading-font-color);
}

#mainmenu li li {
  font-family: var(--body-font);
  font-size: 14px;
}

#mainmenu li li:last-child {
  border-bottom: none;
}

#mainmenu li li a {
  padding: 5px 15px;
  border-top: none;
  color: #606060;
  width: 100%;
  border-left: none;
  text-align: left;
  font-weight: normal;
  border-bottom: solid 1px rgba(255, 255, 255, .1);
  letter-spacing: 0;
}

#mainmenu li li a:hover{
  background: var(--primary-color);
}

#mainmenu li:last-child>a {
  border-bottom: none;
}

.dark-scheme #mainmenu li li a {
  color: #ffffff;
}

.rtl #mainmenu li li a {
  text-align: right;
}

#mainmenu li li a:hover {
  color: #111;
}

#mainmenu li li a:after {
  content: none;
}

#mainmenu li li a:hover,
#mainmenu ul li:hover>a {
  color: #fff;
}

#mainmenu li a.active{
  color:var(--primary-color) !important;
}

#mainmenu li ul {
  width: 190px;
  height: auto;
  position: absolute;
  left: 0px;
  z-index: 10;
  visibility: hidden;
  opacity: 0;
  border-radius:3px;
  -moz-border-radius:3px;
  -webkit-border-radius:3px;
}

#mainmenu ul li b {
  background: #333;
  color: #ffffff;
  padding: 0 3px;
  padding-bottom: 3px;
  font-size: 10px;
  margin-right: 3px;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
}

#mainmenu li li {
  font-size: 14px;
  display: block;
  float: none;
  text-transform: none;
}

#mainmenu li:hover>ul {
  visibility: visible;
  opacity: 1;
  margin-top: 0px;
}

#mainmenu li ul ul:not(.ul-list) {
  top: 0px;
  margin-left: 188px;
}

#mainmenu select {
  padding: 10px;
  height: 36px;
  font-size: 14px;
  border: none;
  background: #ff4200;
  color: #eceff3;
}

#mainmenu select option {
  padding: 10px;
}

#mainmenu .btn-type {
  padding: 0;
  margin: 0;
}

#mainmenu .btn-type a {
  background: #eee;
  padding: 0;
  margin: 0;
}

#mainmenu>li ul.mega {
  position: fixed;
  left: 0;
  height: 0%;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background: none;
}

#mainmenu>li ul.mega .sb-menu {
  background: #fff;
  border-radius: 6px;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  border: solid 1px #dddddd;
}

.dark-scheme #mainmenu>li ul.mega .sb-menu {
  background: var(--bg-dark-1);
  border-color: rgba(255, 255, 255, .1);
}

#mainmenu li:hover ul.mega {
  visibility: visible;
  opacity: 1;
}

#mainmenu li ul.mega>li,
#mainmenu li ul.mega>li a {
  width: 100%;
}

#mainmenu li ul.mega li.title {
  padding: 0px 10px 15px 10px;
  text-align: left;
  color: #fff;
  font-weight: bold;
}

#mainmenu li ul.mega>li ul {
  position: static;
  visibility: visible;
  opacity: 1;
  left: 0;
  float: none;
  width: 100%;
}

#mainmenu ul.mega>li ul li {
  margin: 0;
  padding: 0;
}

#mainmenu ul.mega>li ul li:last-child {
  border-bottom: none;
}

#mainmenu li ul.mega .menu-content {
  background: #171A21;
  padding: 30px;
}

#mainmenu li ul.mega li.title {
  font-family: var(--main-font);
  padding: 0px;
  text-align: left;
  font-weight: bold;
  color: #586383;
}

#mainmenu ul.mega>li ul {
  border: none;
}

#mainmenu li ul.mega ul li a {
  background: none;
  padding: 2px 0;
}

#mainmenu li ul.mega ul li a:hover {
  color: #35404e;
}

header.header-mobile #mainmenu>li ul.mega {
  visibility: visible;
  opacity: 1;
}

header:not(.header-mobile) #mainmenu>li:hover ul.mega {
  height: auto;
}

/* menu toggle for mobile */

#menu-btn {
  display: none;
  float: right;
  width: 32px;
  height: 32px;
  padding: 4px;
  text-align: center;
  cursor: poInter;
  color: #fff;
  margin-top: 0px;
  margin-left: 20px;
}

#menu-btn:before {
  font-family: FontAwesome;
  content: "\f0c9";
  font-size: 20px;
}

#menu-btn.menu-open:before{
  content: "\f068";
}

.rtl #menu-btn {
  float: left;
}


#btn-extra{
  position: relative;
  margin-left: 20px;
  width: 24px;
  height: 12px;
  cursor: pointer;
}

.rtl #btn-extra{
  margin-left: 0;
  margin-right: 20px;
}

#btn-extra span{
  content: '';
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  border-bottom: solid 2px #ffffff;
}

#btn-extra.img{
  width: 32px;
  height: 32px;
}

#btn-extra img{
  width: 100%;
}

header.header-light #btn-extra span{
  border-bottom: solid 2px var(--heading-font-color);
}


#btn-extra span:nth-child(2){
  top:auto;
  bottom: 0;
  right: 0;
}

#btn-extra:hover span{
  width: 70%;
}

.header-light #btn-extra span{
  background: var(--bg-dark-1);
}

#extra-wrap{
  position: fixed;
  z-index: 1002;
  top:0;
  right: -500px;
  width: 500px;
  height: 100%;
  overflow: hidden;
  background: rgba(var(--bg-dark-1-rgb), .75);
  -webkit-backdrop-filter: blur(10px);
  -o-backdrop-filter: blur(10px);
  -moz-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

#extra-wrap.open{
  right: 0;
}

#extra-content{
  padding: 50px;
  color:#ffffff;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: -17px;
  overflow-y: scroll;
}

#btn-close{
  top: 50px;
  right: 50px;
  position: absolute;
  z-index: 1001;
  width: 26px;
  height: 26px;
  cursor: pointer;
}

.rtl #btn-close{
  right: auto;
  left: 50px;
}

#btn-close span{
  content: '';
  display: block;
  position: absolute;
  top: 50%;
  width: 100%;
  height: 2px;
  background: #ffffff;
  transform: rotate(45deg);
}

#btn-close span:nth-child(2){
  transform: rotate(-45deg);
}


header.header-mobile.header-light,
header.header-mobile.header-light #menu-btn,
header.header-mobile.header-light #menu-btn:hover {
  background: none;
}

header.header-mobile.header-light #menu-btn {
  color: #222;
}


header.header-mobile.header-center #logo{
  display: block;
}

header.header-mobile.header-center #logo-center{
  display: none;
}

/* ================================================== */


/* content */


/* ================================================== */

#content {
  width: 100%;
  background: #ffffff;
  padding: 90px 0 90px 0;
  z-index: 100;
}

.dark-scheme #content {
  background: var(--bg-dark-1);
}

.dark-scheme #content {
  background: var(--bg-dark-1);
}

/* ================================================== */


/* section */


/* ================================================== */

section {
  padding: 100px 0 100px 0;
}

#content.zebra section:nth-child(even) {
  background: #fcfcfc;
}

section.no-bg,
#content.no-bg {
  background: none !important;
}

.bottom-rounded{
  overflow: hidden;
  border-radius:0 0 100% 100%;
  -moz-border-radius:0 0 100% 100%;
  -webkit-border-radius:0 0 100% 100%;
  padding-bottom: 200px;
}

.rounded-up{
  border-radius:60px 60px 0 0;
  -moz-border-radius:60px 60px 0 0;
  -webkit-border-radius:60px 60px 0 0;
}

.rounded-down{
  border-radius:0 0 60px 60px;
  -moz-border-radius:0 0 60px 60px;
  -webkit-border-radius:0 0 60px 60px;
}



.rounded-up-down{
  border-radius:60px;
  -moz-border-radius:60px;
  -webkit-border-radius:60px;
}


.rounded-10px{
    border-radius:10px;
    -moz-border-radius:10px;
    -webkit-border-radius:10px;
}

.rounded-20px{
    border-radius:20px;
    -moz-border-radius:20px;
    -webkit-border-radius:20px;
}


.dark-scheme section {
  background: var(--bg-dark-1);
}

.no-bg {
  background: none !important;
}

.bgcolor-variation {
  background: #f6f6f6;
}

.overlay-gradient {
  width: 100%;
  height: 100%;
  z-index: 100;
  top: 0;
  padding: 90px 0 90px 0;
}

.de_light section {
  background: #ffffff;
}

.text_top {
  position: relative;
  z-index: 1000;
}

/* ================================================== */


/* footer */


/* ================================================== */

footer {
  color: var(--body-font-color-dark);
  background: var(--bg-dark-2);
  padding: 100px 0 0 0;
}

footer.footer-light {
  background: #ffffff;
}

footer.footer-light a {
  color: #5d6371;
}

footer p {
  line-height: 1.8em;
}

.dark-scheme footer a {
  color: var(--body-font-color-dark);
}

footer.footer-black {
  background: #222222;
}

footer h5,
.de_light footer h5 {
  color: #fff;
  font-size: 18px;
  text-transform: none;
}

footer a {
  color: var(--body-font-color-dark);
  text-decoration: none !important;
}

footer a:hover{
  color: var(--primary-color);
}

.subfooter {
  margin-top: 50px;
  border-top: solid 1px rgba(255, 255, 255, .1);
  padding: 20px 0 20px 0;
}

.footer-light .subfooter {
  border-top: solid 1px rgba(0, 0, 0, .1);
}

header.header-light-transparent {
  background: rgba(255, 255, 255, .5);
}

header.header-solid.header-light #mainmenu>li>a {
  color: #555;
}

header.header-solid {
  background: #fff;
}

header.smaller.header-dark {
  background: #222222;
  border: none;
}

header.smaller.header-dark #mainmenu a {
  color: #fff;
}

.side-layout header.smaller.header-dark #mainmenu>li {
  border-bottom: solid 1px rgba(255, 255, 255, .1);
}

.side-layout header.smaller.header-dark #mainmenu>li:last-child {
  border-bottom: none;
}

header.header-mobile {
  position: fixed !important;
  background: var(--heading-font-color);
  top: 0;
  padding-bottom: 10px;
}

header.header-mobile .container {
  max-width: 100% !important;
  padding-left: 20px;
  padding-right: 20px;
}

.dark-scheme header.header-mobile {
  background: var(--bg-dark-1);
}

header.header-mobile.has-topbar .header-row,
header.header-mobile.has-topbar .header-col {
  margin-top: 10px;
}

header.header-mobile nav {
  float: none;
}

header.header-mobile .logo {
  display: none;
}

header.header-mobile .logo-2 {
  display: inline-block;
}

header.header-mobile.header-light {
  background: #fff;
}

header.header-mobile #mainmenu ul {
  padding: 0;
}

header.header-solid.header-light {
  background: #fff;
}

header.header-light .logo {
  display: none;
}

header.header-light .logo-2 {
  display: inline-block;
}

/* topbar start here */

#topbar {
  z-index: 1000;
  width: 100%;
  overflow: hidden;
  top: 0;
  background: rgba(var(--bg-dark-1-rgb), .25);
}

#topbar.topbar-light{
  background: var(--bg-grey);
}

#topbar.topbar-light a,
#topbar.topbar-light .social-icons i{
  color: var(--heading-font-color);
}

#topbar.topbar-dark {
  background: rgba(34, 34, 34, .2);
}

#topbar a {
  text-decoration: none;
}

#topbar.text-light a {
  color: #ffffff;
}

.topbar-left,
.topbar-right {
  display: flex;
}

.topbar-right {
  float: right;
}

.topbar-solid {
  background: #ffffff;
}

.tb-light {
  background: #ffffff;
}

#topbar:not(.topbar-noborder) .topbar-right .topbar-widget {
  border-left: solid 1px rgba(255, 255, 255, .1);
}

#topbar:not(.topbar-noborder) .topbar-right span:last-child {
  border-right: solid 1px rgba(255, 255, 255, .1);
}

#topbar:not(.topbar-noborder) .topbar-light .topbar-right .topbar-widget {
  border-left: solid 1px #eee;
}

#topbar:not(.topbar-noborder) .topbar-light .topbar-right span:last-child {
  border-right: solid 1px #eee;
}

#topbar:not(.topbar-noborder) .tb-light .topbar-right .topbar-widget {
  border-left: solid 1px rgba(0, 0, 0, .1);
}

#topbar:not(.topbar-noborder) .tb-light .topbar-right span:last-child {
  border-right: solid 1px rgba(0, 0, 0, .1);
}

/* topbar left content */
.topbar-left {
  float: left;
}

#topbar:not(.topbar-noborder) .topbar-left .topbar-widget {
  border-right: solid 1px rgba(255, 255, 255, .1);
}

#topbar:not(.topbar-noborder) .topbar-light .topbar-left .topbar-widget {
  border-right: solid 1px #eee;
}

#topbar:not(.topbar-noborder) .topbar-left span:first-child {
  border-left: solid 1px rgba(255, 255, 255, .1);
}

#topbar:not(.topbar-noborder) .tb-light .topbar-left .topbar-widget {
  border-right: solid 1px rgba(0, 0, 0, .1);
}

#topbar:not(.topbar-noborder) .tb-light .topbar-left span:first-child {
  border-left: solid 1px rgba(0, 0, 0, .1);
}

.h-phone {
  text-align: center;
  display: inline-block;
  font-weight: 600;
  color: #ffffff;
  font-family: var(--heading-font);
  font-size: 16px;
  margin-right: 20px;
  line-height: 1.3em;
}

.h-phone span {
  display: block;
  font-size: 12.5px;
  font-weight: 500;
  color: rgba(255, 255, 255, .75);
}

header.header-light .h-phone span{
  color: var(--body-font-color);
}


.h-phone i {
  display: inline-block;
  font-size: 32px;
  left: 0;
  top: 12px
}

.menu_side_area .btn-line{
  display: inline-block;
}

/* topbar widget */
.topbar-widget {
  font-size: 14px;
  font-weight: 400;
  display: flex;
  padding: 6px 0  6px 0;
}

.topbar-widget a{
    color:rgba(255, 255, 255, .75);
}

.topbar-widget img{
  width: 20px;
  margin-right: 10px;
}

.rtl .topbar-widget img{
  margin-right: 0;
  margin-left: 10px;
}


.topbar-widget.widget-text {
  padding-top: 6px;
}

.topbar-widget i {
  font-size: 16px;
  margin-top: 8px;
  margin-right: 10px;
  color:var(--primary-color);
}

.h-sub .topbar-widget {
  padding: 12px;
}

.topbar-widget:before {
  float: left;
  position: relative;
  font-family: "FontAwesome";
  font-size: 16px;
  margin-right: 10px;
}

.topbar-widget.tb-phone:before {
  content: "\f095";
}

.topbar-widget.tb-email:before {
  content: "\f003";
}

.topbar-widget.tb-opening-hours:before {
  content: "\f017";
}

.topbar-widget.tb-social {
  padding: 0;
}

.topbar-widget.tb-social a {
  font-size: 14px;
  display: flex;
  padding: 5px;
  padding-left: 15px;
  display: inline-block;
  text-align: center;
}

.topbar-widget a span {
  font-weight: 500;
  margin-right: 10px;
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
}

#topbar:not(.topbar-noborder) .topbar-light .topbar-widget.tb-social a,
#topbar:not(.topbar-noborder) .topbar-light .topbar-widget.tb-social a:first-child {
  border-left: solid 1px rgba(0, 0, 0, .1);
}

#topbar:not(.topbar-noborder) .topbar-widget.tb-social a {
  border-left: solid 1px rgba(255, 255, 255, .1);
}

.tb-light .topbar-widget.tb-social a {
  border-left: solid 1px rgba(0, 0, 0, .1);
}

.topbar-widget.tb-social a:first-child {
  border: none;
}

/* topbar end here */

/* ================================================== */


/* blog */


/* ================================================== */

.blog-list {
  margin: 0;
  padding: 0;
}

/* blog list */

.blog-list>div {
  list-style: none;
  line-height: 1.7em;
  margin-top: 0px;
  border-bottom: solid 1px rgba(255, 255, 255, .1);
  margin-bottom: 20px;
}

.blog-list img {
  margin-bottom: 20px;
}

.de_light .blog-list li {
  border-bottom: solid 1px #ddd;
}

.blog-list h4 {
  margin-top: 20px;
}

.blog-list h4 a {
  color: #fff;
}

.de_light .blog-list h4 a {
  color: #222222;
}

.de_light .text-light .blog-list h4 a {
  color: #fff;
}

.blog-list .btn-more {
  padding: 3px 25px 3px 25px;
  color: #111;
  text-transform: uppercase;
  text-decoration: none;
  font-weight: 700;
  float: right;
}

.blog-list .btn-more:hover {
  background: #fff;
}

.de_light .blog-list .btn-more:hover {
  background: #222;
  color: #fff;
}

.blog-list .post-text {
  padding-left: 80px;
}

.bloglist .post-text a.btn-main {
  display: inline-block;
  margin-top: 0px;
  padding: 5px 25px;
}

.blog-list .blog-slider {
  margin-bottom: -60px;
}

.blog-list .date-box {
  position: absolute;
  text-align: center;
  text-shadow: none;
}

.blog-list .date-box .day,
.blog-list .date-box .month {
  display: block;
  color: #fff;
  text-align: center;
  width: 40px;
  z-index: 100;
}

.de_light .blog-list .date-box .month {
  color: #222222;
}

.blog-list .date-box {
  text-align: center;
  background: #f6f6f6;
  padding: 10px;
}

.blog-list .date-box .month {
  font-size: 14px;
}

.blog-list .date-box .day {
  font-family: var(--body-font);
  color: #222222;
  font-weight: bold;
  font-size: 28px;
}

.blog-list .date-box .month {
  font-family: var(--body-font);
  color: #fff;
  font-size: 13px;
  letter-spacing: 3px;
}

.de_light .blog-list .date-box .month {
  border-bottom-color: #ddd;
}

.de_light .text-light .blog-list .date-box .month {
  color: #fff;
}

/* de-bloglist-type-1 */
.de-bloglist-type-1 .d-content {
  margin-bottom: 10px;
}

.de-bloglist-type-1 .d-image {
  position: absolute;
  overflow: hidden;
  width: 80px;
}

.de-bloglist-type-1 .d-image img {
  width: 100%;
  border-radius:6px;
  -moz-border-radius:6px;
  -webkit-border-radius:6px;
}

.de-bloglist-type-1 .d-content {
  padding-left: 100px;
}

.rtl .de-bloglist-type-1 .d-content {
  padding-left: 0;
  padding-right: 100px;
}


.de-bloglist-type-1 .d-content h4 {
  font-size: 16px;
  margin-bottom: 0px;
}

.de-bloglist-type-1 .d-date {
  position: relative;
  font-size: 12px;
  font-weight: 500;
  padding-left: 18px;
}

.rtl .de-bloglist-type-1 .d-date{
  padding-left: 0;
  padding-right: 18px;
}

.de-bloglist-type-1 .d-date:before {
  font-family: "FontAwesome";
  font-weight: 300;
  color: var(--primary-color);
  content: "\f073";
  left: 0;
  position: absolute;
}

.rtl .de-bloglist-type-1 .d-date:before{
  display: none;
}

.rtl .de-bloglist-type-1 .d-date:after {
  font-family: "FontAwesome";
  font-weight: 300;
  color: var(--primary-color);
  content: "\f073";
  left: auto;
  right: 0;
  position: absolute;
}


/* de-post-type-1 */
.de-post-type-1 {
  margin-bottom: 40px;
}

.de-post-type-1 .d-image {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  margin-bottom: 20px;
}

.de-post-type-1 .d-image img {
  width: 100%;
}

.de-post-type-1 .d-meta {
  font-size: 12px;
  margin-bottom: 10px;
}

.de-post-type-1 .d-meta span {
  display: inline-block;
  font-weight: 600;
  margin-right: 30px;
  padding-left: 30px;
  position: relative;
}

.de-post-type-1 h4 {
  margin-bottom: 10px;
}

.de-post-type-1 h4 a {
  font-size: 20px;
  color: #333333;
  margin-bottom: 0px;
}

.de-post-type-1 .d-meta span:before {
  font-family: "FontAwesome";
  font-size: 20px;
  font-weight: 300;
  color: var(--primary-color);
  content: "";
  left: 0;
  position: absolute;
}

.de-post-type-1 .d-meta span.d-by:before {
  content: "\f007";
}

.de-post-type-1 .d-meta span.d-date:before {
  content: "\f073";
}

.de-post-type-1 .d-meta span.d-tags:before {
  content: "\f02c";
}

.blog-snippet li {
  border-bottom: none;
  margin-bottom: 0;
}

#blog-carousel .item.item {
  width: 100%;
}

.de-blog-c1{
  position: relative;
  margin-bottom: 60px;
}

.de-blog-c1 h4{
  font-size: 28px;
  letter-spacing: 0;
  font-weight: 600;
  line-height: 1.3em;
}

.de-blog-c1 .d-date{
  position: absolute;
  left: 0;
  text-align: center;
  color:#ffffff;
  text-align: center;
}

.de-blog-c1 .d-date .d-month{
  text-transform: uppercase;
  background: var(--primary-color);
  font-size: 16px;
  font-family: var(--heading-font);
  margin-left: .5rem;
  margin-bottom: 10px;
  font-weight:bold;
  line-height: 1em;
  padding: 2px 0;  
  letter-spacing: 2px;
}

.de-blog-c1 .d-date .d-day{
  font-size: 52px;
  font-family: var(--heading-font);
  padding-left: .3rem;
  font-weight: bold;
}

.de-blog-c1 .d-image{
  padding-left: 100px;
}

.de-blog-c1 .d-image img{
  width:100%;
  -webkit-box-shadow: 8px 8px 0 0px var(--primary-color);
  -moz-box-shadow: 8px 8px 0 0px var(--primary-color);
  box-shadow: 8px 8px 0 0px var(--primary-color);
}

/* blog comment */

#blog-comment {
  margin-top: 40px;
}

#blog-comment h5 {
  margin-bottom: 10px;
}

#blog-comment ul,
#blog-comment li {
  list-style: none;
  margin-left: -40px;
  padding-left: 0;
}

.rtl #blog-comment li {
  margin-right: -30px;
}

.rtl #blog-comment li li {
  margin-right: 40px;
}

#blog-comment li {
  min-height: 70px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: solid 1px #ccc;
}

.dark-scheme #blog-comment li {
  border-bottom-color: rgba(255, 255, 255, .1);
}

#blog-comment li .avatar {
  position: absolute;
  margin-top: 5px;
}

#blog-comment li .avatar img{
  width: 70px;
  border-radius:10px;
  -moz-border-radius:10px;
  -webkit-border-radius:10px;
}

#blog-comment .comment {
  margin-left: 85px;
}

.rtl #blog-comment .comment {
  margin-left: 0;
  margin-right: 85px;
}

#blog-comment li {
  min-height: 70px;
  font-size: 15px;
  line-height: 1.6em;
}

#blog-comment li li {
  margin-left: 55px;
  padding-bottom: 0px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: solid 1px #ccc;
  border-bottom: none;
}

.dark-scheme #blog-comment li li {
  border-top-color: rgba(255, 255, 255, .1);
}

#blog-comment li li .avatar {
  position: absolute;
}

#blog-comment li li .comment {}

.comment-info {
  margin-left: 85px;
  margin-bottom: 5px;
}

.rtl .comment-info {
  margin-left: 0;
  margin-right: 85px;
}

.comment-info span {}

.comment-info .c_name {
  display: block;
  font-weight: 700;
  color: #555555;
}

.dark-scheme .comment-info .c_name {
  color: #ffffff;
}

.comment-info .c_reply {
  padding-left: 20px;
  margin-left: 20px;
  border-left: solid 1px #ddd;
  font-size: 12px;
}

.dark-scheme .comment-info .c_reply {
  border-left-color: rgba(255, 255, 255, .2);
}

.comment-info .c_date {
  font-size: 12px;
}

.blog-list h4 {
  color: #333;
}

.blog-list .post-wrapper {}

.blog-read h4 {
  color: #333;
  letter-spacing: normal;
  font-size: 24px;
  text-transform: none;
}

.blog-carousel ul,
.blog-carousel li {
  margin: 0;
  list-style: none;
}

.blog-carousel h4 {
  margin-bottom: 0px;
}

.blog-carousel p {
  margin-bottom: 10px;
}

.blog-carousel .post-date {
  text-transform: uppercase;
  letter-spacing: 3px;
  font-size: 11px;
  color: #888;
}

.bloglist {
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
  background: var(--tertiary-color);
}

.post-content {
  position: relative;
}

.post-content p {
  margin-bottom: 0px;
}

.post-text .d-date{
  font-size: 14px;
}

.post-text h4 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.post-text h4 a {
  font-weight: bold;
  font-family: var(--body-font);
  color: #1A1C26;
  text-decoration: none;
  line-height: 1.5em;
  letter-spacing: 0;
}

.dark-scheme .post-text h4 a {
  color: #ffffff;
}

.post-text .tags {
  font-size: 10px;
  text-transform: uppercase;
}

.p-tagline {
  position: absolute;
  z-index: 1;
  top: 20px;
  left: 20px;
}

.p-tagline {
  left: auto;
  right: 20px;
}

.p-tagline,
.p-title {
  font-family: var(--body-font);
  background: rgba(var(--primary-color-rgb), .2);
  border-radius: 3px;
  display: inline-block;
  font-size: 10px;
  font-weight: bold;
  letter-spacing: 2px;
  line-height: 10px;
  padding: 7px 10px 6px 10px;
  text-transform: uppercase;
  margin-bottom: 5px;
}

.p-tagline,
.p-title.invert {
  background: var(--primary-color);
}

.p-title {
  font-size: 12px;
  margin-bottom: 20px;
}

.post-text .p-date {
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 10px;
  font-weight: 500;
  color: #888888;
  padding: 5px 10px;
  background: rgba(0, 0, 0, .05);
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
}

.blog-read img {
  margin-bottom: 20px;
}

.blog-read .post-text {
  padding: 0;
}

/* blog comment form */

#commentform label {
  display: block;
}

#commentform input {
  width: 290px;
}

#commentform input:focus {
  border: solid 1px #999;
  background: #fff;
}

#commentform textarea {
  width: 97%;
  padding: 5px;
  height: 150px;
  color: #333;
}

#commentform textarea:focus {
  border: solid 1px #999;
  background: #fff;
}

#commentform input.btn {
  width: auto;
}


.post-meta {
  margin: 0px 0 10px 0px;
  border: solid 1px #eee;
  border-left: none;
  border-right: none;
  border-left: none;
  font-size: 11px;
  display: table;
  width: 100%;
  table-layout: fixed;
}

.post-meta a {
  color: #888;
}

.post-meta span {
  display: table-cell;
  padding: 10px 0 10px 0;
  text-align: center;
}

.post-meta span i {
  float: none;
}

.post-image{
  overflow: hidden;x
}
.post-image img {
  width: 100%;
  margin-bottom: 0px;
}
.post-image .d-tagline{
  position: absolute;
  top:20px;
  right: 20px;
  line-height: 1em;
}

.post-image .d-tagline span{
  font-size: 12px;
  color:#ffffff;
  text-transform: uppercase;
  display: inline-block;
  background: var(--bg-dark-1);
  margin-left: 5px;
  padding: 4px 10px 4px 10px;
  border-radius:6px;
  -moz-border-radius:6px;
  -webkit-border-radius:6px;
  font-weight: 600;
}


.blog-slide {
  padding: 0;
  margin: 0;
}

.blog-item {
  margin-bottom: 60px;
}

.blog-item img.preview {
  width: 100%;
  margin-bottom: 30px;
}

.post-image {
  position: relative;
}

.post-image .post-info {
  position: absolute;
  bottom: 0;
  padding: 0;
  border: none;
  width: 100%;
}

.post-image .post-info .inner {
  margin: 0 30px 0 30px;
  padding: 3px 20px;
  border-radius: 0px 10px 0 0;
}

.post-image .post-date,
.post-image .post-author {
  color: #ffffff;
}

/* ================================================== */

/* ratings */


/* ================================================== */

.ratings {
  color: #F86E4E;
}

.p-rating i {
  color: #cccccc;
}

.p-rating i.checked {
  color: #FFD337;
}

.d-rating i {
  font-size: 15px;
  color: #FEC42C;
  margin-right: 3px;
}

/* ================================================== */


/* contact form */


/* ================================================== */

.error {
  display: none;
  margin-top: 30px;
  color: #e7505a;
  clear: both;
  padding-top: 20px;
  padding: 20px;
  background: rgba(231, 80, 90, .1);
  border: solid 1px #e7505a;
}

.success {
  display: none;
  margin-top: 30px;
  clear: both;
  padding-top: 20px;
  padding: 20px;
  background: #96c346;
  border: solid 1px #96c346;
}

.error img {
  vertical-align: top;
}

#contact_form textarea {
  /*height: calc(100% - 20px);*/
}

#contact_form .radio-img input[type="radio"] + label, 
#contact_form .radio-img input[type="checkbox"] + label{
  padding: 0px;
  position: relative;
  background: none;
  width: 20%;
  float: left;
  text-align: center;
  margin-right: 5%;
}

#contact_form .radio-img input[type="radio"] + label:before, 
#contact_form .radio-img input[type="checkbox"] + label:before {
  position: absolute;
  top: 10px;
  left: 15px;
  display: none;
}

#contact_form .radio-img input[type="radio"] + label img, 
#contact_form .radio-img input[type="checkbox"] + label img{
  width: 100%;
  margin-bottom: 10px;
  opacity: .5;
}

#contact_form .radio-img input[type="radio"]:checked + label img, 
#contact_form .radio-img input[type="checkbox"]:checked + label img{
  width: 100%;
  padding: 3px;
  opacity: 1;
  background: var(--primary-color);
}

#contact_form input[type=checkbox] {
  position: relative;
  border: solid 1px var(--primary-color);
  background: none;
  cursor: pointer;
  line-height: 0;
  margin: 4px .6em 0 0;
  outline: 0;
  padding: 0 !important;
  vertical-align: text-top;
  height: 15px;
  width: 15px;
  -webkit-appearance: none;
  opacity: .5;
}

.form-control {
  padding: 10px;
  margin-bottom: 20px;
  border: solid 1px rgba(0,0,0,0.05);
  background: rgba(0,0,0,.025);
  border-radius:6px;
  -moz-border-radius:6px;
  -webkit-border-radius:6px;
}

.text-light .form-control{
  color: #ffffff;
  border: solid 1px rgba(255,255,255,0.2);
  background: rgba(255,255,255,.025);
}

.text-light .form-control option{
  background: var(--bg-dark-1);
}


.radio-opt{
  float: left;
}


.radio-opt input[type="radio"]{
  display: none;
}

.radio-opt label {
  min-width: 62px;
  font-size: 12px;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
  background-color: rgba(var(--primary-color-rgb),.2);
  color: white;
  padding: 0px;
  margin: 0 8px 8px 0;
  opacity: .5;
}
.radio-opt input[type="radio"]:checked + label{
  background-color: var(--primary-color);
  opacity: 1;
}



#contact_form input[type=checkbox]:checked{
  background: var(--primary-color);
  opacity: 1;
}


#contact_form.s2 .radio-img input[type="radio"] + label, 
#contact_form.s2 .radio-img input[type="checkbox"] + label,
#contact_form .radio-img input[type="radio"] + label img, 
#contact_form .radio-img input[type="checkbox"] + label img{
  width: 100%;
  float: none;
}

.form-underline{
  outline: none;
  width: 100%;
  padding: 10px 0 10px 0;
  margin-bottom: 20px;
  border: none;
  border-bottom: solid 1px var(--bg-dark-1);
  background: none;
  border-radius: 0;
  height: auto;
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
}

.text-light .form-underline,
.dark-scheme .form-underline{
  color: #ffffff;
  border-bottom-color: rgba(255, 255, 255, .3);
}

.form-underline input[type=text],
.form-underline textarea,
.form-underline input[type=email],
.form-underline select {
  color: #333;
  border-bottom: solid 1px var(--heading-font-color);
}

.text-light .form-underline input[type=text],
.text-light .form-underline textarea,
.text-light .form-underline input[type=email],
.text-light .form-underline select {
  color: #fff;
  border-bottom: solid 1px #ffffff;
}


.hover-underline{
  position: relative;
  border-bottom: solid 1px rgba(0, 0, 0, .4);
}

.hover-underline .form-underline{
  padding-top: 0;
}

.text-light .hover-underline{
    border-bottom: solid 1px rgba(255, 255, 255, .4);
}


.hover-underline:before{
  content: '';
  position: absolute;
  bottom: 0;
  margin-bottom: -1px;
  width:0;
  border-bottom: solid 1px #000000;
  -o-transition: .3s;
  -ms-transition: .3s;
  -moz-transition: .3s;
  -webkit-transition: .3s;
  transition: .3s;
  outline: none;
}

.text-light .hover-underline:before{
  border-bottom: solid 1px #ffffff;
}


.hover-underline:hover:before{
  width:100%;
}

.text-light ::placeholder {
  color: #ffffff;
  opacity: 1; /* Firefox */
}

.text-light::-ms-input-placeholder { /* Edge 12 -18 */
  color: #ffffff;
}

.form-default input[type=text],
.form-default textarea,
.form-default input[type=email],
.form-default select {
  padding: 10px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, .5);
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  height: auto;
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  font-weight: 400;
  font-size: 18px;
}

.form-border select {
  background: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'><path d='M0 0h24v24H0z' fill='none'></svg>") right 5px center no-repeat !important;
}

.rtl .form-border select {
  background: none !important;
  padding-left: 20px;
}

#contact_form.form-default textarea {
  padding: 10px;
  height: 170px;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
}

#form_subscribe input[type=text] {
  padding: 7px 12px 7px 12px;
  width: 80%;
  float: left;
  display: table-cell;
  border-radius: 30px 0 0 30px;
  -moz-border-radius: 30px 0 0 30px;
  -webkit-border-radius: 30px 0 0 30px;
  border: none;
  border-right: none;
  background: rgba(255, 255, 255, .7);
  -webkit-box-shadow: 2px 2px 20px 0px rgba(20, 20, 20, 0.05);
  -moz-box-shadow: 2px 2px 20px 0px rgba(20, 20, 20, 0.05);
  box-shadow: 2px 2px 20px 0px rgba(20, 20, 20, 0.05);
}

#form_sb input[type=text] {
  font-size: 20px;
  padding: 25px 20px 25px 20px;
  width: 85%;
  height: 30px;
  float: left;
  display: table-cell;
  border-radius: 30px 0 0 30px;
  -moz-border-radius: 30px 0 0 30px;
  -webkit-border-radius: 30px 0 0 30px;
  border: none;
  background: #ffffff;
  border: none;
}

.dark-scheme #form_sb input[type=text] {
  color: #ffffff;
  background: rgba(255, 255, 255, .1);
}

#form_sb #btn-submit i {
  text-align: center;
  font-size: 30px;
  float: left;
  width: 15%;
  background: #171A21;
  color: #ffffff;
  display: table-cell;
  padding: 10px 0 10px 0;
  border-radius: 0 30px 30px 0;
  -moz-border-radius: 0 30px 30px 0;
  -webkit-border-radius: 0 30px 30px 0;
}

footer:not(.footer-light) #form_subscribe input[type=text]::-moz-input-placeholder {
  color: #ffffff;
}

footer:not(.footer-light) #form_subscribe input[type=text]::-webkit-input-placeholder {
  color: #ffffff;
}


footer:not(.footer-light) #form_subscribe.form-dark input[type=text] {
  color: #ffffff;
  background: rgba(255, 255, 255, .1);
}

.footer-light #form_subscribe input[type=text] {
  border: none;
  border-right: none;
  padding: 6px 12px;
  background: #ffffff;
}

#form_subscribe input[type=text]:focus {
  background: rgba(255, 255, 255, .2);
}

#form_subscribe #btn-subscribe i {
  text-align: center;
  font-size: 28px;
  float: left;
  width: 20%;
  background: #171A21;
  color: #ffffff;
  display: table-cell;
  padding: 5px 0 5px 0;
  border-radius: 0 30px 30px 0;
  -moz-border-radius: 0 30px 30px 0;
  -webkit-border-radius: 0 30px 30px 0;
}

.form-default input[type=text]:focus,
.form-default textarea:focus,
.form-default input[type=email]:focus,
.form-default select:focus {
  background: #f6f6f6;
}

.error_input {
  border-bottom: solid 1px #ff0000 !important;
}

.arrow-up,
.arrow-down {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 40px solid #fff;
  position: fixed;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: poInter;
  z-index: 101;
}

.arrow-down {
  border-top: 40px solid #fff;
  border-bottom: none;
  position: fixed;
  z-index: 102;
  top: -40px;
}

.arrow-up:before {
  font-family: "FontAwesome";
  font-size: 20px;
  content: "\f067";
  color: #222;
  position: relative;
  top: 12px;
  margin: 0 0 0 -8px;
}

.arrow-down:before {
  font-family: "FontAwesome";
  font-size: 20px;
  content: "\f068";
  color: #222;
  position: relative;
  top: -35px;
  margin: 0 0 0 -8px;
}

/* ================================================== */


/* elements */


/* ================================================== */


/* border */


/* ------------------------------ */

.small-border {
  width: 50px;
  height: 2px;
  background: rgba(0, 0, 0, .5);
  border-left: none;
  border-right: none;
  display: block;
  margin: 0 auto;
  margin-bottom: 30px;
}


.small-border.sm-left {
  margin-left: 0;
}

.rtl .small-border.sm-left {
  margin-right: 0;
}

.tiny-border {
  height: 1px;
  background: #333;
  width: 40px;
  margin: 20px 0 20px 0;
  display: block;
}

.tiny-border.center {
  margin-left: auto;
  margin-right: auto;
}

.small-border.white {
  border-top: solid 2px #ffffff;
}

/* breadcrumb */


/* ------------------------------ */

.crumb {
  color: #777;
  margin: 0;
  padding: 0;
  font-weight: 500;
}

.crumb{
  padding-top: 10px;
  margin-bottom: -50px;
}

.crumb li {
  display: inline;
  text-decoration: none;
  letter-spacing: 0;
}

.crumb li:after {  
  color: var(--primary-color);
  font-family: "FontAwesome";
  content:"\f054";
  font-size: 10px;
  margin: 0 10px 0 10px;
}

.crumb li:last-child:after {
  display: none;
}

.crumb a {
  opacity: .6;
  text-decoration: none;
}

.crumb a:hover{
  opacity: 1;
}

.crumb li.active {
  color: #ffffff;
  font-weight: bold;
}

.de_light .crumb a:hover {
  color: #fff;
}

/* button and link */


/* ------------------------------ */

a {
  text-decoration: none;
}

.de_light a {
  color: #888;
}

.de_light a:hover {
  color: #555;
}

a.btn-text {
  text-decoration: none;
  display: inline-block;
  color: #111;
  font-weight: 600;
  padding: 0;
}

a.btn-text:after {
  font-family: FontAwesome;
  content: "\f054";
  padding-left: 10px;
}

a.btn-text {
  color: #fff;
}

a.btn-big {
  font-size: 14px;
  color: #eceff3;
  letter-spacing: 1px;
  line-height: normal;
  font-weight: bold;
  text-transform: uppercase;
  border: solid 1px #fff;
  padding: 10px 30px 10px 30px;
}

a.btn-big:after {
  font-family: FontAwesome;
  content: "\f054";
  margin-left: 20px;
}

a.btn,
.btn {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

a.btn:before {
  content: "";
  background: rgba(0, 0, 0, 0);
  width: 0;
  height: 100%;
  position: absolute;
  z-index: -1;
}

a.btn-fx:after {
  font-family: FontAwesome;
  content: "\f178";
  margin-left: 15px;
  position: absolute;
  right: -20px;
  margin-top: 0px;
}

a.btn-fx {
  font-size: 14px;
  color: #eceff3;
  letter-spacing: 1px;
  line-height: normal;
  font-weight: bold;
  text-transform: uppercase;
  border: solid 1px #fff;
  padding: 10px 30px 10px 30px;
}

a.btn-fx:hover {
  padding-left: 20px;
  padding-right: 40px;
}

a.btn-fx:before {
  width: 0;
  height: 100%;
  left: 0;
  top: 0;
  position: absolute;
}

a.btn-fx:hover:after {
  right: 15px;
}

a.btn-fx:hover:before {
  width: 100%;
  background: rgba(0, 0, 0, 1);
}

a.btn-fx.light:hover:before {
  width: 100%;
  background: rgba(255, 255, 255, 1);
}

.btn-fullwidth {
  display: block;
  width: 100%;
}

a.btn-slider {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  line-height: normal;
  text-decoration: none;
  text-transform: uppercase;
  border: solid 2px #fff;
  padding: 10px 30px 10px 30px;
  border-radius: 60px;
}

a.btn-slider:hover {
  color: #222;
  background: #fff;
  border-color: #000;
  border: solid 2px #fff;
}

a.btn-slider:hover:after {
  color: #222;
}

a.btn-id,
a.btn-id:hover {
  border: none;
}

a.btn-light.btn-id {
  color: #222;
}

a.btn-dark.btn-id {
  color: #fff;
}

.btn-main.btn-small {
  padding: 5px 20px 5px 20px;
  font-weight: bold;
  text-transform: uppercase;
}

.btn-fx.btn-main {
  text-transform: normal;
}

a.btn-bg-dark {
  background: #222;
}

a.btn-text-light {
  color: #fff;
}

.btn-icon-left i {
  margin-right: 12px;
  color: #fff;
}

.btn-add_to_cart,
a.btn-add_to_cart {
  border: solid 1px #bbb;
  font-size: 12px;
  border-radius: 0;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  padding: 3px;
  padding-left: 40px;
  padding-right: 20px;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  color: #555;
}

.btn-add_to_cart:before,
a.btn-add_to_cart:before {
  font-family: "FontAwesome";
  content: "\f07a";
  position: absolute;
  left: 20px;
}

a.btn-main,
a.btn-main:active,
a.btn-main:focus,
a.btn-main:visited,
.btn-main,
input[type=button].btn-main,
a.btn-line,
#mainmenu li ul.mega a.btn-main {
  background: var(--primary-color);
  color: var(--btn-color);
  font-family: var(--btn-font-family);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  letter-spacing: var(--btn-letter-spacing);
  padding: var(--btn-padding); 
  text-decoration: var(--btn-text-decoration);
  text-transform: var(--btn-text-transform);
  border-radius: var(--btn-rounded);
  -moz-border-radius: var(--btn-rounded);
  -webkit-border-radius: var(--btn-rounded);
  border: none;
  position: relative;
  display: inline-block;
  text-align: center;
  outline: 0;
  white-space: nowrap;
}

a.btn-main.fx-slide{
  overflow: hidden;
}

a.btn-main.fx-slide span{
  display: block;
  position: relative;
  transition: all .3s ease-in-out;
}

a.btn-main.fx-slide:hover span{
  transform: translate(0, 40px);
  opacity: 0;
}

a.btn-main.fx-slide:hover:before {
  opacity: 1;
  transform: translate(0, 0);
}

a.btn-main.fx-slide:before {
  content: attr(data-hover);
  position: absolute;
  left: 0;
  width: 100%;
  opacity: 0;
  transform: translate(0, -100%);
  transition: all 0.3s ease-in-out;
}

a.btn-main.btn-dark-trans{
    background: rgba(255, 255, 255, .1);
}

a.btn-main.btn-dark-trans:hover{
    background: var(--primary-color);
}


a.btn-main.btn-light-trans{
  color:var(--heading-font-color);
  background: rgba(0, 0, 0, .05);
}

a.btn-main.btn-light-trans:hover{
    color:#ffffff;
    background: var(--primary-color);
}

a.btn-main.big{
  font-size: 30px;
  padding: 20px 30px;
}

a.btn-main.btn-trans.light{
  background: rgba(255, 255, 255, .25);
}

a.btn-topbar{
  display: inline-block;
  padding: 5px 20px;
  font-size: 14px;
  font-weight: bold;
}

a.btn-sc {
  display: inline-block;
  font-family: var(--heading-font);
  font-weight: bold;
  padding: 6px;
  font-size: 14px;
  color: #222;
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
  background: #f2f2f2;
}

a.btn-sc img {
  width: 20px;
  margin-top: -4px;
  margin-right: 8px;
}

a.btn-line,
a.btn-line:hover {
  background: none;
  color: #35404e;
  border: solid 1px rgba(255, 255, 255, .5);
}

.text-light .btn-line{
  color:#ffffff;
}


a.btn-line:hover {
  color: #ffffff;
}

header:not(.header-light) a.btn-line,
.dark-scheme a.btn-line,
.dark-schem a.btn-line:hover {
  color: #ffffff;
}

a.btn-main.btn-white {
  background: #ffffff;
}

.col-right a.btn-main {
  font-size: 14px;
  text-transform: none;
}

a.btn-border {
  border: solid 2px rgba(255, 255, 255, .3);
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  min-width: 120px;
  outline: 0;
  font-weight: bold;
  text-decoration: none;
  padding: 10px 30px;
  min-width: 120px;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 2px;
}

a.btn-border:hover {
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0);
  margin-top: -2px;
  margin-bottom: 2px;
  box-sizing: border-box
}

a.btn-border.light {
  border: solid 1px #ffffff;
  color: #ffffff;
}

a.btn-border.light:hover {
  background: #ffffff;
}

a.btn-border:hover a {
  color: #ffffff !important;
}

.d-btn-close {
  color: #ffffff;
  cursor: poInter;
  text-align: center;
  display: block;
  text-align: center;
  width: 60px;
  height: 60px;
  background: #333;
  font-size: 32px;
  font-weight: bold;
  text-decoration: none;
  margin: 0 auto;
  padding-top: 12px;
  position: absolute;
  left: 0;
  right: 0;
}

a.btn-link {
  display: block;
  text-decoration: none;
  margin-top: 10px;
}

a.btn-lg{
  font-size: 24px;
  padding: 15px 30px 10px 30px;
}

.btn-plus{
  font-size: 15px;
  font-weight: bold;
  display: inline-block;
  position: relative;
  text-align: center;
  max-width: 36px;
  height: 36px;
  border-radius:36px;
  -moz-border-radius:36px;
  -webkit-border-radius:36px;
  overflow: hidden;
  border: solid 1px var(--heading-font-color);
  color: var(--heading-font-color);;
}

.btn-plus:hover{
  max-width: 100%;
  color: var(--heading-font-color);;
}

.btn-plus i{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
}

.btn-plus span{
  opacity: 0;
  display: inline-block;
  overflow: hidden;
  margin-left: 36px;
  line-height: 2.2em;
  padding-right: 15px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btn-plus:hover span{
  opacity: 1;
}


.text-light .btn-plus{
  border: solid 1px #ffffff;
  color: #ffffff;;
}

.text-light .btn-plus:hover{
  color: #ffffff;;
}

.cover a.btn-link:hover {
  color: #fff;
}

.play-button {
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
  color: #fff;
  text-decoration: none;
  border: solid 5px rgba(255, 255, 255, .3);
  display: inline-block;
  text-align: center;
  width: 80px;
  height: 80px;
  padding-top: 22px;
  padding-left: 5px;
}

.play-button:before {
  font-family: "FontAwesome";
  font-size: 20px;
  content: "\f04b";
  position: relative;
  color: #fff;
}

.play-button:hover {
  border: solid 5px rgba(255, 255, 255, 1);
}

.play-button.dark {
  color: #222;
  border: solid 5px rgba(0, 0, 0, .1);
}

.play-button.dark:before {
  color: #222;
}

.play-button.dark:hover {
  border: solid 5px rgba(0, 0, 0, .5);
}

.text-light .play-button {
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
  color: #fff;
  text-decoration: none;
  border: solid 5px rgba(255, 255, 255, .3);
  display: inline-block;
  text-align: center;
  width: 80px;
  height: 80px;
  padding-top: 22px;
  padding-left: 5px;
}

.text-light .play-button:before {
  font-family: "FontAwesome";
  font-size: 20px;
  content: "\f04b";
  position: relative;
  color: #fff;
}

.text-light .play-button.dark {
  color: #fff;
  border: solid 5px rgba(255, 255, 255, .3);
}

.text-light .play-button.dark:before {
  color: #fff;
}

.text-light .play-button.dark:hover {
  border: solid 5px rgba(255, 255, 255, 1);
}



/* divider */


/* ------------------------------ */

.spacer-single {
  width: 100%;
  height: 30px;
  display: block;
  clear: both;
}

.spacer-double {
  width: 100%;
  height: 60px;
  display: block;
  clear: both;
}

.spacer-triple {
  width: 100%;
  height: 90px;
  display: block;
  clear: both;
}

.spacer-half {
  width: 100%;
  height: 15px;
  display: block;
  clear: both;
}


.spacer-5 {
  width: 100%;
  height: 5px;
  display: block;
  clear: both;
}

.spacer-10 {
  width: 100%;
  height: 10px;
  display: block;
  clear: both;
}

.spacer-20 {
  width: 100%;
  height: 20px;
  display: block;
  clear: both;
}

.spacer-30 {
  width: 100%;
  height: 30px;
  display: block;
  clear: both;
}

.spacer-40 {
  width: 100%;
  height: 40px;
  display: block;
  clear: both;
}

.spacer-50 {
  width: 100%;
  height: 50px;
  display: block;
  clear: both;
}

.spacer-60 {
  width: 100%;
  height: 60px;
  display: block;
  clear: both;
}

.spacer-30-line{
  border-bottom: solid 1px rgba(0, 0, 0, .3);
  margin-top: 30px;
  margin-bottom: 30px;
}

.text-light .spacer-30-line{
  border-bottom: solid 1px rgba(255, 255, 255, .3);
  margin-top: 30px;
  margin-bottom: 30px;
}

hr {
  display: block;
  clear: both;
  border-top: solid 1px #ddd;
  margin: 40px 0 40px 0;
}

/* dropcap */


/* ------------------------------ */

.dropcap {
  display: inline-block;
  font-size: 48px;
  float: left;
  margin: 10px 15px 15px 0;
  color: #eceff3;
  padding: 20px 10px 20px 10px;
}

/* form */


/* ------------------------------ */

.form-transparent input[type=text],
.form-transparent textarea,
.form-transparent input[type=email] {
  padding: 10px;
  margin-bottom: 20px;
  color: #fff;
  border: solid 1px rgba(0, 0, 0, .3);
  background: rgba(0, 0, 0, .2);
  border-radius: 0;
  height: auto;
}

.form-border input[type=text],
.form-border textarea,
.form-border input[type=email],
.form-border input[type=password],
.form-border input[type=date],
.form-border select {
  padding: 8px;
  margin-bottom: 10px;
  border: none;
  border: solid 2px #eeeeee;
  background: rgba(0, 0, 0, .025);
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  height: auto;
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  color: #333;
}

.dark-scheme .form-border input[type=text],
.dark-scheme .form-border textarea,
.dark-scheme .form-border input[type=email],
.dark-scheme .form-border input[type=password],
.dark-scheme .form-border input[type=date],
.dark-scheme .form-border select {
  color: #ffffff;
  border: solid 1px rgba(var(--primary-color-rgb), .5);
}

.dark-scheme ::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

.dark-scheme .form-border input::-moz-input-placeholder,
.dark-scheme .form-border textarea::-moz-input-placeholder {
  color: rgba(255, 255, 255, .5);
}

.dark-scheme .form-border input::-webkit-input-placeholder,
.dark-scheme .form-border textarea::-webkit-input-placeholder {
  color: rgba(255, 255, 255, .5);
}

.form-border input[type=text]:focus,
.form-border textarea:focus,
.form-border input[type=date]:focus,
.form-border select:focus {
  background: rgba(var(--primary-color-rgb), .1);
}

.form-subscribe {
  display: table;
  margin: 0 auto;
}

.form-subscribe input[type=text] {
  display: table-cell;
  border: solid 1px rgba(255, 255, 255, .3);
  border-right: none;
  background: rgba(255, 255, 255, .1);
  padding: 6px 20px 6px 20px;
  border-radius: 60px 0 0 60px;
  height: 40px;
  vertical-align: middle;
}

.form-subscribe .btn-main {
  display: table-cell;
  border-radius: 0 60px 60px 0;
  height: 40px;
  vertical-align: middle;
}

.form-subscribe input[type=text]:hover {
  background: rgba(255, 255, 255, .3);
}

.field-set label {
  color: #606060;
}

.field-set .d-label{
  display: inline-block;
  color:var(--heading-font-color);
  font-weight: 500;
  margin-bottom: 10px;
}

.dark-scheme .field-set label {
  color: #ffffff;
}

.de_form input[type="radio"],
.de_form input[type="checkbox"] {
  display: none;
}

.de_form input[type="radio"]+label:before {
  content: "\f111";
  font-family: "FontAwesome";
  margin-right: 10px;
  border: solid 1px #d2d2d2;
}

.de_form input[type="checkbox"]+label:before {
  position: absolute;
  content: '';
  display: inline-block;
  font-family: "FontAwesome";
  margin-right: 10px;
  border: solid 2px rgba(0, 0, 0, .2);
  width: 16px;
  height: 16px;
  margin-top: 5px;
  left: 0;
  font-size: 11px;
  padding: 1px 3px 0 3px;
  line-height: 15px;
  border-radius: 4px;
}

.dark-scheme .de_form input[type="checkbox"]+label:before {
  border: solid 2px rgba(255, 255, 255, .2);
}

.de_checkbox {
  position: relative;
  display: block;
  padding-left: 25px;
}

.de_form input[type="radio"]+label,
.de_form input[type="checkbox"]+label {
  cursor: pointer;
  border: none;
  border-radius: 3px;
}

.no-bg input[type="radio"]+label,
.de_form.no-bg input[type="checkbox"]+label {
  padding: 4px 0px;
  border: none;
  background: none;
}

.de_form input[type="radio"]:checked+label,
.de_form input[type="checkbox"]:checked+label {
  border: none;
}

.de_form input[type="checkbox"]:checked+label:before {
  content: "\f00c";
  color: #ffffff;
  border: rgba(0, 0, 0, 0);
}

.item_filter_group {
  border: solid 1px rgba(0, 0, 0, .15);
  margin-bottom: 25px;
  padding: 30px;
  border-radius: 12px;
  -moz-border-radius: 12px;
  -webkit-border-radius: 12px;
}

.dark-scheme .item_filter_group {
  border: solid 1px rgba(255, 255, 255, .075);
}

.item_filter_group h4 {
  font-size: 18px;
}


/* heading */


/* ------------------------------ */

h1,
h2,
h4,
h3,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
footer.footer-light h5 {
  margin-top: 0;
  font-family: var(--heading-font);
  font-weight: var(--heading-font-weight);
  color: var(--heading-font-color);
  text-transform: var(--heading-text-transform);
}

.dark-scheme h1,
.dark-scheme h2,
.dark-scheme h4,
.dark-scheme h3,
.dark-scheme h5,
.dark-scheme h6,
.dark-scheme .h1_big,
.dark-scheme .h1,
.dark-scheme .h2,
.dark-scheme .h3,
.dark-scheme .h4,
.dark-scheme .h5,
.dark-scheme .h6 {
  color: #ffffff;
}

footer h1,
footer h2,
footer h4,
footer h4,
footer h5,
footer h6 {
  color: #fff;
}

h2.text-light,
footer h4,
footer h4,
footer h5,
footer h6 {
  color: #fff;
}


.text-line{
   color: transparent !important;
  -webkit-text-stroke: 1px #ffffff;
}


span.underline{
  display: inline-block;
  position: relative;
}

span.underline:before{
  content: '';
  position: absolute;
  width: 100%;
  height: .4vw;
  background: var(--primary-color);
  bottom: 0;
  z-index: -1;
}

h2 span.underline:before{
  height: .25vw;
}


h1{
  font-size: var(--h1-font-size);
  font-weight: var(--h1-font-weight);  
  letter-spacing: var(--h1-letter-spacing);
  line-height: var(--h1-line-height);
  margin-bottom: var(--h1-margin-bottom);
}

h2{
  font-size: var(--h2-font-size);
  font-weight: var(--h2-font-weight);  
  letter-spacing: var(--h2-letter-spacing);
  line-height: var(--h2-line-height);
  margin-bottom: var(--h2-margin-bottom);
}

h3{
  font-size: var(--h3-font-size);
  font-weight: var(--h3-font-weight); 
  letter-spacing: var(--h3-letter-spacing);
  line-height: var(--h3-line-height);
  margin-bottom: var(--h3-margin-bottom);
}

h4{
  font-size: var(--h4-font-size);
  font-weight: var(--h4-font-weight); 
  letter-spacing: var(--h4-letter-spacing);
  line-height: var(--h4-line-height);
  margin-bottom: var(--h4-margin-bottom);
}

h5{
  font-size: var(--h5-font-size);
  font-weight: var(--h5-font-weight); 
  letter-spacing: var(--h5-letter-spacing);
  line-height: var(--h5-line-height);
  margin-bottom: var(--h5-margin-bottom);
}


h6{
  font-size: var(--h6-font-size);
  font-weight: var(--h6-font-weight); 
  letter-spacing: var(--h6-letter-spacing);
  line-height: var(--h6-line-height);
  margin-bottom: var(--h6-margin-bottom);
}

h1.title {
  font-size: 64px;
  letter-spacing: 10px;
}

h1.title strong {
  text-transform: none;
  letter-spacing: 0;
  font-weight: normal;
}

h2 .de_light .text-light h2 {
  color: #fff;
}

.text-light h2.deco span:before,
.text-light h2.deco span:after {
  border-top: 1px solid rgba(255, 255, 255, .5);
}


h5.new:before{
  content:"NEW";
  font-size: 12px;
  padding: 1px 6px 2px 6px;
  border-radius:3px;
  -moz-border-radius:3px;
  -webkit-border-radius:3px;
  margin-right: 5px;
  color:#ffffff;
  background: var(--primary-color);
}

h5 span.label {
  display: inline-block;
  margin-right: 10px;
  background: #222222;
  color: #ffffff;
  padding: 0 5px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.wm {
  text-align: center;
  width:150%;
  font-size: 400px;
  font-weight: bold;
  position: absolute;
  left: 0;
  z-index: 0;
  text-align: center;
  font-family: var(--heading-font);
  line-height: 1.5em;
  margin-left:0;
  letter-spacing: -.08em;
  top: 50%;
  background: -webkit-linear-gradient(180deg,rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, .5) 100%);
  background: -moz-linear-gradient(180deg,rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, .5) 100%);
  background: linear-gradient(180deg,rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, .5) 100%);  
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transform: translate(-50%, -50%);
   -webkit-user-select: none;
    user-select: none;
}

.wm .s1{
   -webkit-text-stroke: 0;
   color: rgba(var(--primary-color-rgb),.3);
}

.wm.invert{
  color:rgba(0,0,0,.1);
}

.top-center {
  color: rgba(255, 255, 255, .5);
  position: absolute;
  width:100%;
  left: 0;
  z-index: 1;
  text-align: center;
  font-family: var(--heading-font);
  line-height: 70px;
  letter-spacing: -.05em;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.text-light, .text-light p {
  color: var(--body-font-color-dark);
}

.text-dark, .text-dark p {
  color: var(--body-font-color);
}

.text-white,
.de_light .text-white {
  color: #fff;
}

.text-light h1,
.text-light h2,
.text-light h3,
.text-light h4,
.text-light h5,
.text-light h6 {
  color: #fff;
}

.text-dark h1,
.text-dark h2,
.text-dark h3,
.text-dark h4,
.text-dark h5,
.text-dark h6 {
  color: var(--heading-font-color);
}


.de_light .text-light h1,
.de_light .text-light h2,
.de_light .text-light h4,
.de_light .text-light h4,
.de_light .text-light h5,
.de_light .text-light h6 {
  color: #fff;
}

.text-white h1,
.text-white h2,
.text-white h4,
.text-white h4,
.text-white h5,
.text-white h6 {
  color: #fff;
}

.de_light .text-white h1,
.de_light .text-white h2,
.de_light .text-white h4,
.de_light .text-white h4,
.de_light .text-white h5,
.de_light .text-white h6 {
  color: #fff;
}

.de_light h1,
.de_light h2,
.de_light h4,
.de_light h4,
.de_light h5,
.de_light h6 {
  color: #202020;
}

/* list */

ul.d-inline li{
  list-style: none;
  display: inline;
}

.list-mx-10 li{
  margin: 0 10px;
}

.ul-check {
  padding: 0;
  list-style: none;
}

.ul-check li {
  margin: 5px 0 5px 0;
}

.ul-check li:before {
  font-family: "FontAwesome";
  content: "\f00c";
  margin-right: 15px;
  color:var(--primary-color);
}

.rtl .ul-check li:after {
  font-family: "FontAwesome";
  content: "\f00c";
  margin-left: 15px;
  color:var(--primary-color);
}


.rtl .ul-check li:before{
  display: none;
}

.ul-check.white li:before{
  color: #ffffff;
}

.dark-scheme .ul-check li a{
  color:rgba(255, 255, 255, .7);
}

.ul-style-4 {
  padding: 0;
  list-style: none;
  font-size: 14px;
}

.ul-style-4 li {
  margin: 2px 0 2px 0;
  display: ;
  font-weight: 500;
}

.ul-style-4 li:before {
  color: #ffffff;
  font-family: "FontAwesome";
  content: "\f00c";
  margin-right: 15px;
  padding: 3px;
  font-size: 12px;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
}

.ul-info{
  margin: 0;
  padding: 0;
  list-style: none;
}

.ul-info li{
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: solid 1px rgba(0, 0, 0, .1);
}

.ul-info li div:nth-child(2){
  font-weight: 600;
}

ol.ol-style-1 {
  list-style-type: none;
  counter-reset: ordered;
  margin-left: 50px;
}

.rtl ol.ol-style-1 {
  margin-right: 50px;
  margin-left: 0px;
}

ol.ol-style-1 li {
  position: relative;
  font-weight: 500;
  margin-bottom: 20px;
  font-size: 18px;
}

ol.ol-style-1.fs-16 li{
  font-size: 16px;
}

ol.ol-style-1 li::before {
  background: rgba(var(--primary-color-rgb),1);
  border-radius: 50%;
  color: #ffffff;
  content: counter(ordered);
  counter-increment: ordered;
  display: inline-block;
  font-weight: 400;
  margin-left: -50px;
  margin-top: -5px;
  padding: 5px 5px;
  position: absolute;
  top:10px;
  width: 40px;
  height: 40px;
  text-align: center;
}

.rtl ol.ol-style-1 li::before {
  display: none;
}

.rtl ol.ol-style-1 li::after{
  background: rgba(var(--primary-color-rgb),1);
  border-radius: 50%;
  color: #ffffff;
  content: counter(ordered);
  counter-increment: ordered;
  display: inline-block;
  font-weight: 400;
  margin-right: -50px;
  margin-top: -5px;
  padding: 5px 5px;
  position: absolute;
  top:10px;
  right: 0;
  width: 40px;
  height: 40px;
  text-align: center;
}

ol.ol-style-1.c2 li::before{
  background: var(--bg-dark-2);
}

ol.ol-style-1 h4{
  font-size: 18px;
  margin-bottom: 10px;
}

.text-light ol.ol-style-1 li::before{
  color: #ffffff;
}

.ol-style-1 {
  font-size: 16px;
  font-weight: bold;
}

.ol-style-1 h4 {
  font-size: 16px;
}

.ol-style-1 p {
  font-weight: normal;
}

.ol-style-1 {
  list-style-type: none;
  counter-reset: item;
  margin: 0;
  padding: 0;
}

.ol-style-1 li {
  display: table;
  counter-increment: item;
  margin-bottom: 0.6em;
}

.ol-style-1 li:before {
  content: counters(item, ".") ". ";
  display: table-cell;
  padding-right: 0.6em;
}

.ol-style-1 li li {
  margin: 0;
}

.ol-style-1 li li:before {
  content: counters(item, ".") " ";
}

/* pagination */

.page-link{
  margin: 0 5px;
  font-weight: bold;
  color: var(--heading-font-color);
  background: none;
}

.page-link{
  width: 40px;
  border: none;
  border-radius:100%;
  -moz-border-radius:100%;
  -webkit-border-radius:100%;
}

.page-link:hover{
  background: none;
}

.page-item.active .page-link{
  border-color:var(--primary-color);
}

.dark-scheme .page-link{
  color: #ffffff;
}

.spinner {
  display: table-cell;
  vertical-align: middle;
  margin: 100px auto 0;
  width: 70px;
  text-align: center;
}

.spinner>div {
  width: 18px;
  height: 18px;
  background-color: #fff;

  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0)
  }

  40% {
    -webkit-transform: scale(1.0)
  }
}

@keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
  }
}

/* ================================================== */

/* preloader */

/* ================================================== */

#de-loader {
  position: fixed;
  z-index: 20000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dark-scheme #de-loader{
  background: var(--bg-dark-1);
}

.lds-roller {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.lds-roller div {
  animation: lds-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  transform-origin: 40px 40px;
}

.lds-roller div:after {
  content: " ";
  display: block;
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: var(--primary-color);
  margin: -2px 0 0 -2px;
}

.lds-roller div:nth-child(1) {
  animation-delay: -0.036s;
}

.lds-roller div:nth-child(1):after {
  top: 63px;
  left: 63px;
}

.lds-roller div:nth-child(2) {
  animation-delay: -0.072s;
}

.lds-roller div:nth-child(2):after {
  top: 68px;
  left: 56px;
}

.lds-roller div:nth-child(3) {
  animation-delay: -0.108s;
}

.lds-roller div:nth-child(3):after {
  top: 71px;
  left: 48px;
}

.lds-roller div:nth-child(4) {
  animation-delay: -0.144s;
}

.lds-roller div:nth-child(4):after {
  top: 72px;
  left: 40px;
}

.lds-roller div:nth-child(5) {
  animation-delay: -0.18s;
}

.lds-roller div:nth-child(5):after {
  top: 71px;
  left: 32px;
}

.lds-roller div:nth-child(6) {
  animation-delay: -0.216s;
}

.lds-roller div:nth-child(6):after {
  top: 68px;
  left: 24px;
}

.lds-roller div:nth-child(7) {
  animation-delay: -0.252s;
}

.lds-roller div:nth-child(7):after {
  top: 63px;
  left: 17px;
}

.lds-roller div:nth-child(8) {
  animation-delay: -0.288s;
}

.lds-roller div:nth-child(8):after {
  top: 56px;
  left: 12px;
}

@keyframes lds-roller {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* progress bar */


/* ================================================== */

.de-progress {
  width: 100%;
  height: 8px;
  background: #ddd;
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  overflow: hidden;
}

.de-progress .progress-bar {
  background: #333;
  height: 8px;
  box-shadow: none;
}

.de-progress {
  background: #ffffff;
}

.skill-bar {
  margin-bottom: 40px;
}

.skill-bar.style-2 .de-progress {
  background: rgba(0, 0, 0, .1);
}

.skill-bar .value {
  color: #ccc;
  font-size: 12px;
  font-weight: bold;
  float: right;
  margin-top: -40px;
}

.rtl .skill-bar .value {
  float: left;
}

.skill-bar h4 {
  font-size: 16px;
}


/* social-icons */


/* ================================================== */

.social-icons {
  display: inline-block;
}

.social-icons i {
  text-shadow: none;
  padding: 10px 5px;
  width: 36px;
  height: 36px;
  text-align: center;
  font-size: 16px;
  margin: 0 3px 0 3px;
  border-radius:6px;
  -moz-border-radius:6px;
  -webkit-border-radius:6px;
}

.dark-scheme .social-icons i {
  background: rgba(255, 255, 255, .1);
}

.social-icons i.fa-google-plus {
  padding: 12px 14px 10px 6px;
}

.social-icons.s2 i{
  background: rgba(255, 255, 255, .1);
}

.social-icons i:hover {
  background: #fff;
  border-color: #eceff3;
  color: #333;
}

.social-icons.big a{
  padding: 0;
  display: inline-block;
}
.social-icons.big i {
  font-size: 26px;
  width: auto;
  height: auto;
  color:#ffffff;
  background: var(--primary-color);
  border-radius:0px;
  -moz-border-radius:0px;
  -webkit-border-radius:0px;
  width: 48px;
  padding: 14px 0;
}

.social-icons.big i:hover {
  background: none;
}

#topbar .social-icons i{
  font-size: 16px;
  background: none;
  margin-bottom: 0;
  margin-right: 1px;
  padding: 15px 5px;
  height: 30px;
  width: 30px;
  margin-top: 5px;
  color:#ffffff;
}

.social-icons.big i {
  margin: 0 15px 0 15px;
}

.social-icons.big i:hover {
  background: none;
}

.social-icons.s1 i {
  border: none;
  font-size: 16px;
  margin: 3px;
}

.social-icons.s1:hover i {
  background: none;
}

.social-icons.s1 {
  line-height: 0.5;
  text-align: center;
}

.social-icons.s1 .inner {
  display: inline-block;
  position: relative;
}

.social-icons.s1 .inner:before,
.social-icons.s1 .inner:after {
  content: "";
  position: absolute;
  height: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, .2);
  top: 0;
  width: 100%;
}

.social-icons.s1 .inner:before {
  right: 100%;
  margin-right: 15px;
  margin-top: 20px;
}

.social-icons.s1 .inner:after {
  left: 100%;
  margin-left: 15px;
  margin-top: 20px;
}

.col-right {
  display: inline-block;
  float: right;
  margin-top: 22px;
}

/* ================================================== */

/* accordion

/* ================================================== */


.accordion-section-title {
  color: #35404e;
  width: 100%;
  padding: 15px 0;
  cursor: poInter;
  font-family: var(--body-font);
  width: 100%;
  display: inline-block;
  font-size: 17px;
  transition: all linear 0.5s;
  text-decoration: none;
  font-weight: 600;
  overflow: none;
  border-bottom: solid 1px #dddddd;
}

.dark-scheme .accordion-section-title,
.text-light .accordion-section-title {
  color:#ffffff;
  border-bottom: solid 1px rgba(255, 255, 255,.4) !important;
}

.accordion-section-title:before {
  font-family: "FontAwesome";
  content: "\f107";
  float: right;
  color: var(--bg-dark-1);
  text-align: center;
  padding: 0 8px 0 8px;
  font-size: 15px;
  border-radius: 8px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  width: 30px;
}

.dark-scheme .accordion-section-title:before,
.text-light .accordion-section-title:before{
  color: #ffffff;
}

.rtl .accordion-section-title:before {
  float: left;
}

.accordion-section-title.active:before {
  content: "\f106";
}

.accordion-section-title.active {
  margin-bottom: 0px;
  text-decoration: none;
}

.accordion-section-title:hover {
  text-decoration: none;
}

.accordion-section-content {
  padding-top: 15px;
  padding-bottom: 5px;
  display: none;
  margin-bottom: 10px;
}

.accordion.s2 .accordion-section-title{
  border:none;
  border-bottom: solid 1px #ddd;
}


/* bs */

.accordion-item h2 {
  line-height: 24px;
  display: block;
}

.expand-custom {
  background: #ffffff;
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 3px;
  border: solid 1px #dddddd;
}

.dark-scheme .expand-custom {
  background: var(--bg-color-odd);
  border: var(--border-default);
}
.de_tab .de_nav {
  overflow: hidden;
  padding-left: 0;
  margin: 0;
  padding: 0;
  font-weight: bold;
}

.de_tab .de_nav li {
  float: left;
  list-style: none;
  margin-right: 10px;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
}

.rtl .de_tab .de_nav li {
  float: right;
  margin-right: 0;
  margin-left: 0;
}

.d_coll .de_tab .de_nav {
  text-align: center;
}

.d_coll .de_tab .de_nav li {
  float: none;
  display: inline-block;
  margin-right: 5px;
}

.de_tab .de_nav li span {
  border: solid 1px rgba(0, 0, 0, .2);
  display: block;
  text-decoration: none;
  color: #646464;
  font-size: 15px;
  cursor: poInter;
}

.dark-scheme .de_tab .de_nav li span {
  color: rgba(255, 255, 255, .3);
  background: #292F45;
}

.de_tab.tab_methods .de_nav li span {
  border: solid 2px rgba(0, 0, 0, .1);
}

.de_tab.tab_methods .de_nav li {
  background: none;
  display: inline-block;
  float: none;
  margin: 0 10px 0 0;
}

.de_tab.tab_methods .de_nav li span {
  padding: 35px 10px;
  color: rgba(0, 0, 0, .6);
  font-family: var(--body-font);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  border-radius: 12px;
  min-width: 140px;
}

.dark-scheme .de_tab.tab_methods .de_nav li span {
  color: rgba(255, 255, 255, .6);
}

.dark-scheme .de_tab.tab_methods .de_nav li.active span {
  color: #ffffff;
}


.de_tab.tab_methods .de_nav li span i {
  display: block;
  font-size: 30px;
  margin-bottom: 10px;
}

.de_tab.tab_methods.style-2 .de_nav li span {
  background: none;
  border-radius: 0px;
  border: solid 1px rgba(255, 255, 255, .5);
  font-size: 16px;
  color: #fff;
}

.de_tab.tab_methods.style-2.light .de_nav li span {
  background: #eee;
  color: #707070;
}

.de_tab.tab_methods.style-2 .de_nav li.active span {
  border: solid 2px rgba(255, 255, 255, .8);
  color: #222;
}

.de_tab.tab_methods .de_nav li .v-border {
  height: 60px;
  border-left: solid 1px rgba(255, 255, 255, .3);
  display: inline-block;
  margin-bottom: -5px;
}

.de_tab.tab_methods.dark .de_nav li .v-border {
  border-left: solid 1px rgba(0, 0, 0, .3);
}

.de_tab .de_nav li span {
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.de_tab .de_tab_content {
  padding-top: 30px;
}

.de_tab.tab_methods .de_tab_content {
  background: 0;
  padding: 0;
  border: none;
  padding-top: 30px;
}

.de_tab.tab_methods.dark .de_tab_content {
  border-top: solid 1px rgba(0, 0, 0, .4);
}

.de_tab .de_nav li.active span {
  background: #FFF;
  color: #222;
}

.de_tab .de_nav li.active span .v-border {
  display: none;
}

.de_tab .de_nav li.active span {
  background: #27282b;
}

.de_tab.tab_methods .de_nav li.active span {
  background: none;
}

.de_tab.tab_methods.dark .de_nav li.active span {
  background: none;
  color: #333;
  border: solid 1px rgba(0, 0, 0, .8);
}

/* new added */

.de_tab.tab_methods.tab_6 .de_nav li span {
  padding: 20px 0 20px 0;
  font-size: 14px;
  line-height: 70px;
  width: 120px;
  height: 120px;
}

.de_tab.tab_methods.style-2 .de_nav li .arrow {
  width: 0;
  height: 0;
  border-left: 0px solid transparent;
  border-right: 0px solid transparent;
  border-top: 0px solid transparent;
  margin: 0 auto;
  margin-bottom: 20px;
}

.de_tab.tab_methods.style-2 .de_nav li.active .arrow {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid;
  margin: 0 auto;
}

.de_tab.tab_methods.style-2 .de_nav li {
  margin: 5px;
  display: inline-block;
}

.de_tab.tab_methods.style-2 .de_nav li span {
  font-family: var(--body-font);
  background: rgba(255, 255, 255, .1);
  border-radius: 0px;
  font-size: 16px;
  font-weight: lighter;
  color: #fff;
  opacity: .8;
  width: 120px;
  height: 100px;
  border: none;
  padding: 20px;
  border-radius: 300px;
  -moz-border-radius: 300px;
  -webkit-border-radius: 300px;
  -webkit-border-radius: 300px;
}

.de_tab.tab_methods.style-2 .de_nav li i {
  display: block;
  font-size: 36px;
  margin-bottom: 10px;
}

.de_tab.tab_methods.style-2.sub-style-2 .de_nav li span {
  background: rgba(0, 0, 0, .1);
  color: #000;
  opacity: .8;
}

.de_tab.tab_methods.style-2 .de_nav li.active span {
  color: #222;
  opacity: 1;
  border: none;
}

.de_tab.tab_methods.style-2 .de_nav li span:hover {
  border: none;
}

.de_tab.tab_methods.style-2 .de_tab_content {
  border: none;
}

.de_tab.tab_simple .de_nav li {
  min-width: 80px;
  text-align: center;
}

.de_tab.tab_simple .de_nav li span {
  font-size: 16px;
  font-weight: 600;
  padding: 2px 0px;
  margin-right: 30px;
  border: none;
  border-radius: 0;
  background: none;
  border-bottom: solid 2px rgba(255, 255, 255, 0);
  color: rgba(0, 0, 0, .5);
}

.dark-scheme .de_tab.tab_simple .de_nav li span {
  color: rgba(255, 255, 255, .5);
}

.rtl .de_tab.tab_simple .de_nav li span {
  margin-right: 0px;
  margin-left: 30px;
}

.de_tab.tab_simple .de_nav li.active span {
  color: rgba(0, 0, 0, 1);
}

.dark-scheme .de_tab.tab_simple .de_nav li.active span {
  color: rgba(255, 255, 255, 1);
}



/* tab de_light */

.de_light .de_tab.tab_style_2 .de_tab_content {
  border: none;
  background: none;
  padding: 40px 0 0 0;
  border-top: solid 1px #ddd;
}

.text-light .de_tab.tab_style_2 .de_tab_content {
  border-top: solid 1px rgba(255, 255, 255, .1);
}

.de_light .de_tab.tab_style_2 .de_nav {}

.de_light .de_tab.tab_style_2 .de_nav li {
  float: none;
  list-style: none;
  background: none;
  margin: 0 5px 0 5px;
  text-align: center;
  display: inline-block;
}

.de_light .de_tab.tab_style_2 .de_nav li img {
  width: auto;
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
  opacity: .7;
}

.de_light .de_tab.tab_style_2 .de_nav li.active img {
  width: auto;
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  filter: grayscale(0%);
  opacity: 1;
}

.de_light .de_tab.tab_style_2 .de_nav li span {
  background: none;
  display: block;
  text-decoration: none;
  font-size: 14px;
  cursor: poInter;
  border: none;
}

.de_light .de_tab.tab_style_2 .de_nav li.active span {
  border-bottom: solid 6px;
  color: #333;
  background: none;
}

.text-light .de_tab.tab_style_2 .de_nav li.active span {
  color: #fff;
}

/* tab de_light */

.de_light .de_tab.tab_style_3 .de_tab_content {
  border: none;
  background: none;
  padding: 40px 0 0 0;
  border-top: solid 1px #ddd;
}

.text-light .de_tab.tab_style_3 .de_tab_content {
  border-top: solid 1px rgba(255, 255, 255, .1);
}

.de_light .de_tab.tab_style_3 .de_nav {
  text-align: center;
  background: #eee;
  display: inline-block;
  border-radius: 60px;
  margin-bottom: 30px;
  padding: 0;
}

.de_light .de_tab.tab_style_3 .de_nav li {
  float: none;
  list-style: none;
  background: none;
  text-align: center;
  display: inline-block;
  margin: 0;
  padding: 0;
}

.de_light .de_tab.tab_style_3 .de_nav li img {
  width: auto;
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
  opacity: .7;
}

.de_light .de_tab.tab_style_3 .de_nav li.active img {
  width: auto;
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  filter: grayscale(0%);
  opacity: 1;
}

.de_light .de_tab.tab_style_3 .de_nav li span {
  background: none;
  display: block;
  text-decoration: none;
  font-size: 14px;
  cursor: poInter;
  border: none;
  padding: 5px 30px 5px 30px;
  min-width: 150px;
}

.de_light .de_tab.tab_style_3 .de_nav li:last-child span {
  border-radius: 0 60px 60px 0;
}

.de_light .de_tab.tab_style_3 .de_nav li:first-child span {
  border-radius: 60px 0 0 60px;
}

.de_light .de_tab.tab_style_3 .de_nav li span:hover {
  background: #ddd;
}

.de_light .de_tab.tab_style_3 .de_nav li.active span {
  background: #eee;
  color: #fff;
}

.text-light .de_tab.tab_style_3 .de_nav li.active span {
  color: #fff;
}

.de_tab h3{
  font-size: 18px;
  margin-bottom: 10px;
}

.de_tab h3 span{
  font-size:40px ;
}

.de_tab p{
  margin-bottom: 0;
}

.de_nav h3{
  text-transform: uppercase;
  font-size: 24px;
  line-height: 1em;
  margin-bottom: 0;
}


/* tab de_light */

.de_tab.tab_style_4 .de_tab_content {
  border: none;
  background: none;
  padding: 0;
  border-top: none;
}

.text-light .de_tab.tab_style_4 .de_tab_content {
  border-top: none;
}

.de_tab.tab_style_4 .de_nav {
  text-align: center;
  display: inline-block;
  border-radius: 60px;
  margin-bottom: 30px;
  padding: 0;
  border: solid 1px rgba(255, 255, 255, .5);
}

.de_tab.tab_style_4 .de_nav li img {
  width: auto;
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
  opacity: .7;
}

.de_tab.tab_style_4 .de_nav li.active img {
  width: auto;
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -o-filter: grayscale(0%);
  -ms-filter: grayscale(0%);
  filter: grayscale(0%);
  opacity: 1;
}

.de_tab.tab_style_4 .de_nav li {
  display: inline-block;
  text-decoration: none;
  font-size: 14px;
  cursor: poInter;
  border: none;
  padding: 15px 30px 15px 30px;
  min-width: 150px;
  margin: 0;
  border-right: solid 1px rgba(255, 255, 255, .5);
}

.de_tab.tab_style_4 .de_nav li span {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  width: auto;
  display: inline-block;
}

.de_tab.tab_style_4 .de_nav li:last-child {
  border-radius: 0 60px 60px 0;
  border-right: none;
}

.de_tab.tab_style_4 .de_nav li:first-child {
  border-radius: 60px 0 0 60px;
}

.de_tab.tab_style_4 .de_nav li.active,
.de_tab.tab_style_4 .de_nav li.active span {
  background: #333;
  color: #fff;
}

.text-light .de_tab.tab_style_4 .de_nav li.active {
  color: #fff;
}

.de_tab.tab_style_4 .de_nav li.active h4,
.de_tab.tab_style_4 .de_nav li.active h4,
.de_tab.tab_style_4 .de_nav li.active h4 span {
  color: #fff;
}

.tab_style_4 h4,
.de_tab.tab_style_4 .de_nav li span {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
}

.tab_style_4 h4 {
  margin: 0;
  font-size: 12px;
}

.de_tab .de_tab_content.tc_style-1 {
  background: none;
  padding: 0;
  padding-top: 30px;
}

.tab-small-post ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.tab-small-post ul li {
  min-height: 60px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: solid 1px rgba(0, 0, 0, .1);
}

.tab-small-post ul li {
  border-bottom: solid 1px #202220;
}

.tab-small-post ul li:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.tab-small-post ul li img {
  position: absolute;
}

.tab-small-post ul li span {
  display: block;
  padding-left: 64px;
}

.tab-small-post ul li span.post-date {
  font-size: 11px;
}

.de_tab.timeline.nav_4 li {
  display: inline-block;
  text-align: center;
  background: none;
  width: 24%;
}

.de_tab.timeline.nav_4 li span {
  background: none;
  border: none;
  margin-top: 20px;
  font-family: var(--body-font);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.de_tab.timeline.nav_4 .dot {
  display: block;
  width: 8px;
  height: 8px;
  background: #fff;
  opacity: 1;
  margin: 0 auto;
  border-radius: 8px;
  margin-top: 1px;
}

.de_tab.timeline.nav_4 .dot:before,
.de_tab.timeline.nav_4 .dot:after {
  content: "";
  position: absolute;
  height: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, .1);
  top: 0;
  width: 24%;
}

.de_tab.timeline.nav_4 li:last-child .dot:before,
.de_tab.timeline.nav_4 li:last-child .dot:after {
  border-bottom: none;
}

.de_tab.timeline li .num {
  font-size: 20px;
}

.de_tab.timeline .de_tab_content {
  background: none;
  border: none;
  padding-top: 30px;
}

.de_tab .navigation_arrow {
  text-align: center;
}

.btn-left,
.btn-right {
  background: none;
  border: solid 1px rgba(255, 255, 255, .2);
  padding: 10px 20px 10px 20px;
  margin: 5px;
  font-family: var(--body-font);
  text-transform: uppercase;
  letter-spacing: 5px;
}

.btn-right:after {
  font-family: FontAwesome;
  content: "\f054";
  padding-left: 10px;
}

.btn-left:before {
  font-family: FontAwesome;
  content: "\f053";
  padding-right: 10px;
}

a.btn-left,
a.btn-right {
  text-decoration: none;
}

/* testimonial
    /* ================================================== */

.de_testi {
  display: block;
  margin-bottom: 10px;
}

.de_testi blockquote p {
  margin-top: 10px;
}

/*
.de_testi blockquote:before {
  font-family: FontAwesome;
  content: "\f10d";
  padding-bottom: 10px;
  font-size: 20px;
  display: inline-block;
  padding: 10px 20px 10px 20px;
  font-style: normal;
  background: #000;
  position: absolute;
  left: 15px;
  font-weight: normal;
}
*/

.de_testi blockquote .de-rating-ext{
  text-align: center;
  margin-top: 0;
}

.de_testi blockquote p {
  text-align: center;
  font-size: 16px;
  line-height: 1.7em;
  font-weight: 400;
  margin-bottom: 10px;
}

.de_testi_by div{
  color:var(--heading-font-color);
  font-weight: bold;
  margin-top: 20px;
  display: block;
  text-align: center;
  font-style: normal;
  font-size: 15px;
  line-height: 1.5em;
}

.de_testi_by div span{
  display: block;
  font-weight: 400;
  font-size: 15px;
  opacity: .5;
}

.de_testi_by img {
  width: 80px !important;
  height: 80px !important;
  display: block;
  margin: 0 auto;
  margin-top: 20px;
  border-radius: 100%;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
}

.de_testi_pic {
  float: left;
  padding-right: 15px;
}

.de_testi_company {
  padding-top: 20px;
}

.de_testi blockquote {
  position: relative;
  display: block;
  font-family: var(--body-font);
  border: none;
  font-weight: 300;
  padding: 10px;
}

.de_testi.s2 blockquote{
  background: #ffffff;
  padding: 20px 40px 40px 40px;
  border-radius:var(--rounded-1);
  -moz-border-radius:var(--rounded-1);
  -webkit-border-radius:var(--rounded-1);
}

.text-light .de_testi.s2 blockquote{
  background: var(--bg-dark-1);
  color:#ffffff;
}

.de_testi.no-bg blockquote {
  background: none;
}

.de_testi blockquote:before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f10e";
  color: rgba(255,255,255,.05);
  z-index: 100;
  font-size: 84px;
  background: none;
  top:20px;
  right:0;
  padding: 20px;
  z-index:-1;
}


.de_testi.type-2.review blockquote {
  padding: 50px;
}

.de_testi.type-2.review blockquote:before {
  display: none;
}

.de_testi.type-2.review p {
  padding-top: 0;
}

.de_testi.type-2.review h3 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 18px;
}

.text-light .de_testi.type-2 blockquote {
  color: #fff;
  background: var(--bg-color-odd);
  border: var(--border-default);
}

.de_testi.type-2 blockquote p {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.7em;
  letter-spacing: -.1px;
}

.de_testi.type-2 .de_testi_by {
  font-size: 14px;
  font-weight: normal;
}

.de_testi.opt-3 blockquote {
  background: none;
  color: #555;
  padding-top: 0;
  font-weight: 400;
}

.de_testi.opt-3 blockquote p {
  font-size: 14px;
}

.de_testi.opt-3 blockquote:before {
  color: #fff;
}

.de_testi.opt-3 .de_testi_by {
  font-size: 14px;
  color: #000;
}

.de_testi.de-border blockquote {
  border: solid 5px rgba(0, 0, 0, .05);
}

/* new timeline */

.site-timeline-content{
  position: relative;
}

.de-timeline-article .site-center-line {
  position: absolute;
  width: 2px;
  height: 100%;
  top: 5%;
  left: 50%;
  margin-left: -2px;
  bottom: 5%;
}
.year__ {
  display: inline-block;
}
.de-timeline-article.odd .site-center-line {
  background: var(--primary-color);
}
.de-timeline-article.even .site-center-line {
  background: var(--primary-color);
}
.de-timeline-article {
  width: 100%;
  position: relative;
  overflow: hidden;
  margin: 0;
  margin-top: -2%;
}
.de-timeline-article .content-left-container,
.de-timeline-article .content-right-container {
  max-width: 47%;
  width: 100%;
  margin-bottom: 80px;
}
.de-timeline-article .content-left,
.de-timeline-article .content-right {
  position: relative;
  width: auto;
  padding: 60px 25px 20px;
}
.de-timeline-title {
  margin-top: 0;
}
.de-timeline-article p {
  padding: 0;
  font-size: 15px;
  position: relative;
}
.de-timeline-article .content-left-container {
  float: left;
}
h4.de-timeline-year {
  margin: 0;
  display: inline-block;
  padding: 10px 25px;
  border-radius: 21px;
  color: #fff;
}
.de-timeline-article.even h4.de-timeline-year{
  color:var(--heading-font-color);
}
.de-timeline-article.odd .year__ {
  float: left;
}
.de-timeline-article.odd h4.de-timeline-year {
  background: var(--primary-color);
}
.de-timeline-article.even .year__ {
  float: right;
}
.de-timeline-article.even h4.de-timeline-year {
  background: var(--primary-color);
}
.de-timeline-article .d-line {
  position: absolute;
  width: 55px;
  height: 2px;
  top: 20px;
  margin-left: 0;
}
.de-timeline-article.odd .d-line {
  left: 50%;
  background: var(--primary-color);
}
.de-timeline-article.even .d-line {
  right: 50%;
  background: var(--primary-color);
}
.de-timeline-article .owl-carousel {
  position: relative;
  width: auto;
  padding: 60px 25px 20px;
}
.de-timeline-article .content-right-container {
  float: right;
}
.de-timeline-article .meta-dot {
  position: absolute;
  top: 10px;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-left: -10px;
  border-radius: 100%;
}
.de-timeline-article.odd .meta-dot {
  background: var(--primary-color);
}
.de-timeline-article.even .meta-dot {
  background: var(--primary-color);
}
@media only screen and (max-width: 830px) {
  .de-timeline-article .site-center-line {
    margin-left: 0;
    top: 30px;
    left: 30px;
  }
  .de-timeline-article .meta-dot {
    margin-left: 0;
    left: 20px;
  }
  .de-timeline-article .content-left-container,
  .de-timeline-article .content-right-container {
    max-width: 100%;
    width: auto;
    float: none;
    margin-left: 50px;
  }
  .de-timeline-article .content-left-container {
    margin-bottom: 20px;
  }
  .de-timeline-article .content-left,
  .de-timeline-article .content-right {
    padding: 10px 25px;
    min-height: 65px;
  }
  .de-timeline-article .content-left:before {
    content: "\f0d9";
    right: auto;
    left: -8px;
  }
  .de-timeline-article .content-right:before {
    display: none;
  }  
  h4.de-timeline-year {
    position: absolute;
    top:0;
    left: 60px;
  }
  .de-timeline-article.odd .d-line {
    left: 30px;
  }
  .de-timeline-article.even .d-line {
    right: 0;
    left: 30px;
  }
  .de-timeline-article.odd .content-left-container{
    padding-top: 20px;
  }
  .de-timeline-article.even .content-left-container{
    padding-top: 50px;
  }
  .de-timeline-article.odd .content-left-container .owl-carousel{
    padding: 60px 20px 0 20px;
  }
   .de-timeline-article.even .content-right-container .owl-carousel{
    padding: 0px 20px 60px 20px;
  }
}

/* close new timeline */


.v-center {
  min-height: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: #ffffff;
}


.d-flex-2 {
  display: flex;
  align-items: center;
}

.timeline-area {
  padding: 80px 0;
}

.all-timelines {
  position: relative;
}

.timelines h2 {
  text-align: center;
  color: #fff;
  font-weight: 600;
  margin-bottom: 40px;
}

.all-timelines::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  height: 100%;
  width: 2px;
  background: #efa22f;
  top: 20px;
}

.single-timeline {
  margin-bottom: 22px;
}

.timeline-blank {
  width: 50%;
}

.timeline-text {
  width: 50%;
  padding-left: 30px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}

.timeline-text h6 {
  color: #f0f1f2;
  font-weight: 900;
  display: inline-block;
  font-size: 1rem;
}

.timeline-text span {
  color: #f0f1f2;
  display: block;
  width: 100%;
}

.single-timeline:nth-child(even) .timeline-text span {
  text-align: right;
}

.t-circle {
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  left: -6px;
  background: #efa22f;
  border-radius: 100%;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
}

.single-timeline:nth-child(even) {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.single-timeline:nth-child(even) .t-circle {
  right: -6px;
  left: unset;
}

.single-timeline:nth-child(even) .timeline-text {
  padding-left: 0;
  padding-right: 30px;
  text-align: right;
}

@media all and (max-width: 768px) {
  .all-timelines::before {
    right: unset;
    top: 0;
  }

  .single-timeline:nth-child(2n) .timeline-text {
    padding-left: 30px;
    padding-right: 0;
    text-align: left;
  }

  .single-timeline:nth-child(2n) .t-circle {
    left: -6px;
    right: unset;
  }

  .timeline-blank {
    display: none;
  }

  .timeline-text {
    width: 100%;
  }

  .single-timeline:nth-child(even) .timeline-text span {
    text-align: left !important;
  }
}

@media all and (max-width: 360px) {
  .all-timelines::before {
    top: 32px;
  }
}



/* css attributes */

.overflow-hidden {
  overflow: hidden;
}

.border1 {
  border-width: 1px;
}

.bordertop {
  border-top: solid 1px #eee;
}

/* ================================================== */


/* anim */


/* ================================================== */

header,
.hover,
.hover *,
.de_tab .de_nav li span,
a.btn-line:after,
.de_count,
.social-icons a i,
.de_tab.tab_methods .de_nav li span,
.de_tab.tab_methods .de_nav li span:hover,
.mfp-close,
.mfp-close:hover,
.text-rotate-wrap,
.text-rotate-wrap .text-item,
.carousel-item .pf_text .title,
.carousel-item:hover .pf_text .title,
.carousel-item .pf_text .title .type,
.carousel-item:hover .pf_text .title .type,
.owl-slider-nav .next,
.owl-slider-nav .prev,
#back-to-top.show,
#back-to-top.hide,
.social-icons-sm i,
.social-icons-sm i:hover,
.de_tab .de_nav li span,
#popup-box.popup-show,
#popup-box.popup-hide,
.d-btn-close,
.btn-main,
.de_tab.tab_style_4 .de_nav li,
.form-subscribe input[type=text],
#mainmenu *,
#mainmenu a span,
#mainmenu a.active span,
.error_input,
.de_tab.tab_methods.style-2 .de_nav li.active .arrow,
.owl-item.active.center div blockquote,
.mask *,
.mask .cover,
.mask:hover .cover,
.mask img,
.mask:hover img,
.accordion .content,
a.btn-border,
#contact_form input,
#form_subscribe input[type=text],
.logo-carousel img,
a.a-underline *,
.dropdown *,
.form-border *,
.d-carousel *,
#slider-carousel *,
.container-timeline *,
.d_demo_img *,
.carousel-control-next-icon,
.carousel-control-prev-icon,
.grayscale,
.hover-color,
.show-on-scroll,
#mainmenu>li::before,
.btn-plus,
.btn-plus *,
#topbar,
#extra-wrap,
#btn-extra,
#btn-extra *,
.crumb *,
.active-show,
.de-dot *,
#selector *,
.de__pcard *,
#de_notif{
  -o-transition: .5s;
  -ms-transition: .5s;
  -moz-transition: .5s;
  -webkit-transition: .5s;
  transition: .5s;
  outline: none;
}

a.btn-fx:after,
a.btn-fx:hover:after,
a.btn-fx:before,
a.btn-fx:hover:before,
#mainmenu li,
#mainmenu li ul,
.widget-post li .post-date,
.items_filter *,
.owl-prev,
.owl-next {
  -o-transition: .2s ease;
  -ms-transition: .2s ease;
  -moz-transition: .2s ease;
  -webkit-transition: .2s ease;
  transition: .2s ease;
  outline: none;
}

header.header-mobile #mainmenu>li>span {
  -o-transition: none;
  -ms-transition: none;
  -moz-transition: none;
  -webkit-transition: none;
  transition: none;
  outline: none;
}

@-webkit-keyframes scaleIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(1.5, 1.5, 1.5);
    transform: scale3d(1.5, 1.5, 1.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(1.5, 1.5, 1.5);
    transform: scale3d(1.5, 1.5, 1.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.scaleIn {
  -webkit-animation-name: scaleIn;
  animation-name: scaleIn;
}

@-webkit-keyframes scaleInFade {
  from {
    opacity: 0;
    -webkit-transform: scale3d(1.5, 1.5, 1.5);
    transform: scale3d(1.5, 1.5, 1.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes scaleInFade {
  from {
    opacity: 0;
    -webkit-transform: scale3d(1.5, 1.5, 1.5);
    transform: scale3d(1.5, 1.5, 1.5);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.scaleInFade {
  -webkit-animation-name: scaleInFade;
  animation-name: scaleInFade;
}

@-webkit-keyframes scaleOutFade {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1, 1, 1);
    transform: scale(1, 1, 1);
  }
}

@keyframes scaleOutFade {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1, 1, 1);
    transform: scale(1, 1, 1);
  }
}

.scaleOut {
  -webkit-animation-name: scaleOutFade;
  animation-name: scaleOutFade;
}

@-webkit-keyframes widthInFade {
  from {
    opacity: 0;
    width: 1px;
  }

  100% {
    opacity: 1;
    width: 100px;
  }
}

@keyframes widthInFade {
  from {
    opacity: 0;
    width: 1px;
  }

  100% {
    opacity: 1;
    width: 100px;
  }
}

.widthInFade {
  -webkit-animation-name: widthInFade;
  animation-name: widthInFade;
}

/* ================================================== */


/* video */


/* ================================================== */

video,
object {
  display: inline-block;
  vertical-align: baseline;
  min-width: 100%;
  min-height: 100%;
}

.de-video-container {
  top: 0%;
  left: 0%;
  height: 500px;
  width: 100%;
  overflow: hidden;
}

.de-video-content {
  width: 100%;
  position: absolute;
  z-index: 10;
}

.de-video-overlay {
  position: absolute;
  width: 100%;
  min-height: 100%;
  background: rgba(20, 20, 20, .5);
}

.video-fixed {
  width: 100%;
  position: fixed;
  top: 0;
}

/* youtube, vimeo */

.video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}

.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.de-video-container .btn-line:hover:after,
.de-video-container a.btn-line:hover:after,
.de-video-container a.btn-line.hover:after {
  color: #eceff3;
}

/* ================================================== */


/* map */


/* ================================================== */


/* --------------- map --------------- */

#map {
  width: 100%;
  height: 500px;
}

.map iframe {
  width: 100%;
  border: solid 1px #ccc;
  padding: 2px;
  background: #fff;
}

.map-container {
  width: 100%;
  height: 380px;
  overflow: hidden;
}

.map-container iframe {
  width: 100%;
}

.de-map-hotspot {
  position: relative;
}

.de-map-hotspot img {
  width: 100%;
}

.de-spot {
  padding: 0;
  margin: 0;
  position: absolute;
  vertical-align: center;
  text-align: center;
}


.de-spot span {
  position: absolute;
  display: inline-block;
  background: #ffffff;
  padding: 0 10px;
  font-size: 12px;
  font-weight: bold;
  left: 30px;
  line-height: 1.8em;
  color: #35404e;
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
}
.dark-scheme .de-spot span{
  background: none;
  color:#ffffff;
  border: solid 2px rgba(255, 255, 255, .25);
}
.de-circle-1 {
  width: 15px;
  height: 15px;
  background-color: var(--tertiary-color);
  border-radius: 50%;
  position: absolute;
  margin: 5px 0 0 5px;
  left: 31%;
}

.de-circle-2 {
  border: 4px solid var(--tertiary-color);
  -webkit-border-radius: 30px;
  height: 25px;
  width: 25px;
  position: absolute;
  -webkit-animation: pulsate 1s ease-out;
  -webkit-animation-iteration-count: infinite;
  opacity: 0.0;
  left: 31%;
}

@-webkit-keyframes pulsate {
  0% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0.0;
  }

  50% {
    opacity: 1.0;
  }

  100% {
    -webkit-transform: scale(1.2, 1.2);
    opacity: 0.0;
  }
}

/* ================================================== */


/* overide bootstrap */


/* ================================================== */

.dark-scheme .modal-header,
.nav-tabs {
  border-bottom-color: rgba(255, 255, 255, .2);
}

.dark-scheme .modal-content {
  background: var(--bg-dark-1);
}

.dark-scheme .modal-footer {
  border-top-color: rgba(255, 255, 255, .2);
}

.dark-scheme .accordion-body {
  background: var(--bg-dark-1);
  border-color: #292F45;
}

.nav-tabs .nav-link {
  font-weight: 600;
}

.tab-default .tab-content {
  margin-top: 20px;
}

.container-fluid {
  padding: 0px;
  margin: 0px;
}

.btn-primary {
  text-shadow: none;
  border: none;
}

.btn.btn-primary:hover {
  background: #555;
}

.img-responsive {
  display: inline-block;
}

.img-shadow {
  -webkit-box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.1);
}

.img-circle{
  border-radius:100%;
  -moz-border-radius:100%;
  -webkit-border-radius:100%;
}

.img-gray{
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
}

img.img-auto {
  width: auto;
  height: auto;
}

img.img-rounded {
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.images-group-s1 img {
  width: 31.5%;
  margin-right: 2.5%;
  float: left;
  padding: 5px;
}

.images-group-s1 img:nth-child(2) {
  padding-top: 25%;
}

.images-group-s1 img:nth-child(3) {
  padding-top: 12.5%;
  margin-right: 0;
}

.box-rounded {
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.img-card-sm {
  width: 72px;
  background: #ffffff;
  margin: 3px;
  padding: 5px 0;
}

p{
  margin-top: 0;
}

p.lead {
  font-size: 18px;
  line-height: 32px;
  margin-top: 0;
  font-weight: 400;
  letter-spacing: -0.2px;
}

p.small{
  line-height: 1.8em;
}

p.lead.big{
  font-size: 32px !important;
}

.dark-scheme p.lead {
  color: rgba(255, 255, 255, .75);
}

.dark-scheme strong{
  color:#ffffff;
}

.panel-group {
  opacity: .8;
}

.panel-heading,
.panel-body {
  background: #27282B;
  border: solid 1px #202220;
  border-radius: 0;
}

.panel-body {
  border-top: none;
}

.progress {
  background: #27282B;
}

.panel-heading h4 a {
  display: block;
}

.panel-heading h4 a:hover {
  color: #eceff3;
}

.panel-default,
.panel-collapse,
.panel-collapse,
.panel-group .panel {
  border: none;
  border-radius: 0;
}

.panel-heading {
  background: #222;
}

.table-bordered,
.table-bordered td {
  border: solid 1px #202220;
}

blockquote {
  font-family: var(--body-font);
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.4em;
  border: none;
  border-left: solid 3px #333333;
  padding-left: 30px;
}

.rtl blockquote {
  border-left: none;
  border-right: solid 3px #333333;
  padding-left: 0px;
  padding-right: 30px;
}

.dark-scheme blockquote {
  color: #ffffff;
}

.dark-scheme blockquote span {
  color: #ffffff;
}

blockquote.s2 {
  border: none;
  text-align: left;
  background: #f2f2f2;
  padding: 30px 30px 30px 60px;
  margin-top: 20px;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
}

blockquote.s2:before {
  font-family: "FontAwesome";
  content: "\f10e";
  font-style: normal;
  position: absolute;
  margin-left: -30px;
}

blockquote.s1 {
  padding: 0px;
}

blockquote.s1:before {
  font-family: "FontAwesome";
  content: "\f10d";
  display: block;
  color: rgba(0, 0, 0, .1);
  z-index: -1;
  font-size: 40px;
  top: 0;
  left: 0;
}


.collapsible-link::before {
  content: '';
  width: 14px;
  height: 2px;
  background: #333;
  position: absolute;
  top: calc(50% - 1px);
  right: 1rem;
  display: block;
  transition: all 0.3s;
}

/* Vertical line */
.collapsible-link::after {
  content: '';
  width: 2px;
  height: 14px;
  background: #333;
  position: absolute;
  top: calc(50% - 7px);
  right: calc(1rem + 6px);
  display: block;
  transition: all 0.3s;
}

.text-light .collapsible-link:before,
.text-light .collapsible-link:after {
  background: #ffffff;
}



.rtl .collapsible-link::before {
  right: auto;
  left: 0px;
}

.rtl .collapsible-link::after {
  right: auto;
  left: 6px;
}

.rtl .rtl-show {
  display: block
}

.rtl .rtl-hide {
  display: none;
}

.collapsible-link[aria-expanded='true']::after {
  transform: rotate(90deg) translateX(-1px);
}

.collapsible-link[aria-expanded='true']::before {
  transform: rotate(180deg);
}

.collapsible-link.text-white {
  color: #ffffff;
}

.collapsible-link.text-white::after,
.collapsible-link.text-white::before {
  background: #ffffff;
}


.accordion-style-1 {
  overflow: visible;
}

.accordion-style-1 .card {
  margin-bottom: 20px;
  border: none;
  -webkit-box-shadow: 8px 8px 50px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 8px 8px 50px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 8px 8px 50px 0px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  overflow: hidden;
}

.pic-grey {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
}

.pic-grey:hover{
  -webkit-filter: grayscale(0);
  -moz-filter: grayscale(0);
  -o-filter: grayscale(0);
  -ms-filter: grayscale(0);
  filter: grayscale(0);
}

/* ================================================== */


/* widget */


/* ================================================== */

.widget {
  margin-bottom: 30px;
  padding-bottom: 30px;
}

footer .widget{
  margin-bottom: 0;
}


.widget ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.widget ul li {
  margin: 3px 0;
}

.widget .small-border {
  height: 2px;
  width: 30px;
  background: #fff;
  margin: 0px 0 20px 0;
  display: block;
}

.widget_category li {
  padding: 8px 20px 8px 20px;
  margin-bottom: 8px;
  background: #111;
}

.widget_category li:before {
  font-family: "FontAwesome";
  content: "\f07b";
  margin-right: 10px;
}

.de_light .widget_category li {
  background: none;
  border: solid 1px #eee;
}

.widget_category li a {
  color: #606060;
}

.widget_recent_post li a {
  color: #555;
}

.widget-post li {
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.widget-post li a {
  font-weight: 600;
  display: block;
  text-decoration: none;
  color: #606060;
}


.dark-scheme .widget-post li a {
  color: #ffffff;
}

.widget-post .post-date {
  position: absolute;
  color: #222;
  margin-right: 10px;
  text-align: center;
  width: 50px;
  font-size: 10px;
  text-transform: uppercase;
}

.widget-post li:hover>.post-date {
  background: #222;
  color: #fff;
}

.widget-post .date {
  margin-top: 5px;
  margin-right: 10px;
  float: left;
  background: #333;
  color: #fff;
  display: inline-block;
  padding: 2px 3px 2px 3px;
  line-height: 1.4em;
  font-size: 12px;
  width: 60px;
  font-weight: bold;
  text-align: center;
}

.rtl .widget-post .date {
  float: right;
}

.widget .tiny-border {
  width: 100%;
  background: rgba(255, 255, 255, .1);
}

.footer-light .widget .tiny-border {
  background: rgba(0, 0, 0, .1);
}

.widget_tags li {
  text-shadow: none;
  display: inline-block;
  margin-right: 3px;
  margin-bottom: 16px;
}

.widget_tags li a {
  font-size: 14px;
  text-decoration: none;
  color: #fff !important;
  padding: 5px 10px 7px 10px;
  margin: 0 2px;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  background: var(--bg-dark-1);
}

.de_light .widget_tags li a {
  color: #606060;
  border: solid 1px rgba(0, 0, 0, .2);
}

.widget_tags li a:hover {
  color: #555;
  border-color: #555;
}

.de_light .widget_tags li a:hover {
  color: #111;
  border-color: #111;
}

.widget_tags li a {
  border-color: #555;
}

.widget_tags li a:hover border-color: #eceff3;
}

footer .widget {
  border: none;
  margin-bottom: 0;
}

.widget_tags ul {
  margin-top: 30px;
}

footer .widget.widget_tags li a {
  border-color: #222;
}

footer .widget_recent_post li {
  padding: 0 0 10px 0;
  margin: 0;
  margin-bottom: 10px;
  background: none;
  border-bottom-color: rgba(255, 255, 255, .1);
}

footer .widget_recent_post li a {
  color: #ccc;
}

footer .widget h5 {
  font-size: 18px;
  margin-bottom: 20px;
}

footer .widget_list li {
  padding: 0 0 10px 0;
  margin: 0;
  margin-bottom: 10px;
  background: none;
  border-bottom-color: rgba(255, 255, 255, .1);
}

footer .widget_list li a {
  color: #ccc;
}

footer .tiny-border span {
  background: #ffffff;
  display: block;
  height: 1px;
  width: 30px;
}

#cookit {
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 1rem 2rem;
  display: flex;
  z-index: 1022;
  background: rgba(20, 20, 20, 1) !important;
}

.dark-scheme #cookit {
  border-top: solid 1px rgba(255, 255, 255, .15);
}

#cookit.hidden {
  display: none;
}

#cookit #cookit-container {
  margin: auto;
  width: 1300px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

#cookit #cookit-container>* {
  margin: .4rem;
}

#cookit #cookit-container p {
  line-height: 1.4rem;
}

#cookit #cookit-container a {
  text-decoration: none;
}

#cookit #cookit-container a:hover {
  opacity: .9;
}

#cookit #cookit-container #cookit-button {
  color: #ffffff !important;
  font-size: 14px;
  font-weight: bold;
  padding: .25rem 2rem;
  margin-left: auto;
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
}

/* marquee */


.d-flex {
  display: flex;
  white-space: nowrap;
}

.de-marquee-list {
  display: flex;
  align-items: top;
  -webkit-animation: loop 40s infinite linear;
  animation: loop 40s infinite linear;
}

.de-marquee-list .d-item-txt {
  font-size: 100px;
  line-height: 100px;
  padding-right: 8px;
  font-style: normal;
  font-weight: bold;
  font-family: var(--heading-font);
  color: var(--primary-color);
  user-select: none;
  text-transform:uppercase;
   color: transparent;
  -webkit-text-stroke: 1px rgba(255, 255, 255, .75);
}

.de-marquee-list.s2 .d-item-txt{
  background: -webkit-linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
  background: -moz-linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
  background: linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 0;
}

.de-marquee-list .d-item-txt img{
  width: 80px;
  margin-top: -15px;
  margin-right: 20px;
}

.de-marquee-list .d-item-display {
  display: inline;
}

.de-marquee-list .d-item-block {
  width: 80px;
  height: 2px;
  margin: 0 20px;
  margin-right: 40px;
  padding: 0px;
  border-radius: 50%;
  display: inline-block;
  transform: translateY(-30px);
  background: var(--primary-color);
}

.de-marquee-list.s2 .d-item-block{
  height: 5px;
  background: -webkit-linear-gradient(90deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
  background: -moz-linear-gradient(90deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
  background: linear-gradient(90deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
}

.de-marquee-list .d-item-block{
  border-radius: 0;
}

.text-light .de-marquee-list .d-item-dot {
  background: rgba(255, 255, 255, .3);
}

.de-marquee-list.style-2 .d-item-txt {
  font-size: 160px;
  background: rgba(255, 255, 255, .2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.de-marquee-list.style-2 .d-item-dot {
  transform: translateY(-0px);
  background: rgba(255, 255, 255, .2);
}

/* marquee */


.de-step-s1 {
  overflow: ;
  position: relative;
  padding: 0 25px;
  height: 100%;
  border-radius:var(--rounded-1);
  -moz-border-radius:var(--rounded-1);
  -webkit-border-radius:var(--rounded-1);
}

.de-step-s1 p {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.de-step-s1 h4 {
  position: relative;
  z-index: 1;
}

.de-step-s1 .d-number {
  font-size: 14px;
  text-transform: uppercase;
  font-weight:500;
  padding: 5px 10px;
  display: inline-block;
  border-radius:5px;
  -moz-border-radius:5px;
  -webkit-border-radius:5px;
  margin-bottom: 40px;
  line-height: 1em;
  color:var(--heading-font-color);
}

.text-light .de-step-s1 .d-number{
  color:#ffffff;
}

.border-gray {
  border: solid 1px #ddd;
}

.bg-blur {
  background: rgba(0, 0, 0, .15);
  -webkit-backdrop-filter: blur(10px);
  -o-backdrop-filter: blur(10px);
  -moz-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

/* marquee plugin close */

.gradient-edge-top {
  z-index: 1;
  position: absolute;
  top: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(180deg, rgba(var(--bg-dark-1-rgb), 1) 0%, rgba(var(--bg-dark-1-rgb), 0) 100%);
}


.gradient-edge-top.light {
  background: linear-gradient(180deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
}


.dark-scheme .gradient-edge-top {
  background: linear-gradient(180deg, rgba(var(--bg-dark-1-rgb), 1) 0%, rgba(var(--bg-dark-1-rgb), 0) 100%);
}

.gradient-edge-bottom {
  z-index: 1;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(0deg, rgba(var(--bg-dark-1-rgb), 1) 0%, rgba(var(--bg-dark-1-rgb), 0) 100%);
}

.gradient-edge-bottom.color{
  background: linear-gradient(0deg, rgba(var(--primary-color-rgb), 1) 0%, rgba(var(--primary-color-rgb), 0) 100%);
}

.gradient-edge-bottom.light {
  background: linear-gradient(0deg, rgba(255,255,255, 1) 0%, rgba(255,255,255, 0) 100%);
}

.dark-scheme .gradient-edge-bottom {
  background: linear-gradient(0deg, rgba(var(--bg-dark-1-rgb), 1) 0%, rgba(var(--bg-dark-1-rgb), 0) 100%);
}


.no-border {
  border: none !important;
}


.owl-carousel.no-hide .owl-stage-outer{
  overflow: visible; !important;
}

.grayscale{
  filter: grayscale(1);
}

.hover-color:hover{
  filter:grayscale(0);
}

.show-on-scroll{
  opacity: 0;
}

.show-on-scroll.show{
  opacity: 1;
}

.show-on-scroll.hide{
  opacity: 0;
}

.d_wrap{
    position: relative;
}
.d_wrap img{
    position: relative;
}
.d_wrap_sm-box{
    font-size: 15px;
    font-family: var(--heading-font);
    font-weight: bold;
    display: inline-block;
    position: absolute;
    background: rgba(30, 31, 34, .5);   
    padding: 10px 15px;
    color: #2f394b;
    box-shadow: 5px 25px 20px 5px rgba(0,0,0,0.1);
    -webkit-box-shadow: 5px 25px 20px 5px rgba(0,0,0,0.1);
    -moz-box-shadow: 5px 25px 20px 5px rgba(0,0,0,0.1);
    border-radius: 8px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    padding-left: 60px;
}

.d_wrap_sm-box h6, .d_wrap_sm-box h4{
  color:var(--heading-font-color);
}

.de-rating-ext .d-val{
  color:#fff;
  margin-right: 10px;
}

.de-rating-ext{
  font-size: 14px;
}

.de-rating-ext i{
  color:#F2B827;
}

/* select2 begin */
.select2-container--default .select2-selection--single {
  border-color: #fff;
  height: 60px;
  padding: 2.5px 0;
  border-radius: 0;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 48px;
}

.select2-dropdown {
  border-radius: 0;
  box-shadow: #444 0px 3px 5px;
  border: 0;
}


.select2-container--default{
    margin-bottom: 20px;
}
.select2-container--default img{
    width: 30px;
    margin-left: 5px;
    margin-right:10px;
    padding: 7px 0;
}
.select2-container--default .select2-selection--single{
    background: none;
    height: 50px;
    border: solid 1px rgba(255, 255, 255, .2);
    border-radius:10px;
    -moz-border-radius:10px;
    -webkit-border-radius:10px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
    color:#ffffff;
}
.select2-container--default .select2-results__option--selectable{
    background: #333;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable{
    background: var(--primary-color);
}

/* select2 end */

.grid-divider > [class*=col-]:nth-child(n+2):after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  border-left: solid 1px rgba(255, 255, 255, .3);
  height: auto;
}

.grid-divider.dark > [class*=col-]:nth-child(n+2):after {
  border-left: solid 1px rgba(0, 0, 0, .25);
}


.grid-divider.dark-divider > [class*=col-]:nth-child(n+2):after {
  border-left: solid 1px var(--heading-font-color);
}

.dark-scheme .grid-divider > [class*=col-]:nth-child(n+2):after,
.text-light .grid-divider > [class*=col-]:nth-child(n+2):after {
  border-left-color: rgba(255, 255, 255, .5);
}

/* ================================================== */

/* declass */

/* ================================================== */

/* background */
.bg-white{ background:#ffffff; }
.bg-black{ background:#000000; }

/* width (percent) */
.w-10{width: 10%;}
.w-20{width: 20%;}
.w-30{width: 30%;}
.w-40{width: 40%;}
.w-50{width: 50%;}
.w-60{width: 60%;}
.w-70{width: 70%;}
.w-80{width: 80%;}
.w-90{width: 90%;}
.w-100{width: 100%;}

/* width (px) */
.w-10px{width: 10px;}
.w-20px{width: 20px;}
.w-24px{width: 24px;}
.w-30px{width: 30px;}
.w-40px{width: 40px;}
.w-50px{width: 50px;}
.w-60px{width: 60px;}
.w-70px{width: 70px;}
.w-80px{width: 80px;}
.w-90px{width: 90px;}
.w-100px{width: 100px;}
.w-110px{width: 110px;}
.w-120px{width: 120px;}
.w-130px{width: 130px;}
.w-140px{width: 140px;}
.w-150px{width: 150px;}
.w-200px{width: 200px;}
.w-250px{width: 250px;}
.w-300px{width: 300px;}
.w-400px{width: 400px;}
.w-500px{width: 500px;}

.h-auto{height: auto;}

/* height (percent) */
.h-10{height: 10%;}
.h-20{height: 20%;}
.h-30{height: 30%;}
.h-40{height: 40%;}
.h-50{height: 50%;}
.h-60{height: 60%;}
.h-70{height: 70%;}
.h-80{height: 80%;}
.h-90{height: 90%;}
.h-100{height: 100%;}

/* height (px) */
.h-10px{height: 10px;}
.h-20px{height: 20px;}
.h-24px{height: 24px;}
.h-40px{height: 40px;}
.h-50px{height: 50px;}
.h-60px{height: 60px;}
.h-70px{height: 70px;}
.h-80px{height: 80px;}
.h-90px{height: 90px;}
.h-100px{height: 100px;}
.h-110px{height: 110px;}
.h-120px{height: 120px;}
.h-130px{height: 130px;}
.h-140px{height: 140px;}
.h-150px{height: 150px;}
.h-200px{height: 200px;}
.h-250px{height: 250px;}
.h-300px{height: 300px;}

/* top (percent) */
.top-10{top: 10%;}
.top-20{top: 20%;}
.top-30{top: 30%;}
.top-40{top: 40%;}
.top-50{top: 50%;}
.top-60{top: 60%;}
.top-70{top: 70%;}
.top-80{top: 80%;}
.top-90{top: 90%;}
.top-100{top: 100%;}

/* top (px) */
.top-10px{top: 10px;}
.top-20px{top: 20px;}
.top-30px{top: 30px;}
.top-40px{top: 40px;}
.top-50px{top: 50px;}
.top-60px{top: 60px;}
.top-70px{top: 70px;}
.top-80px{top: 80px;}
.top-90px{top: 90px;}
.top-100px{top: 100px;}

/* bottom (percent) */
.bottom-10{bottom: 10%;}
.bottom-20{bottom: 20%;}
.bottom-30{bottom: 30%;}
.bottom-40{bottom: 40%;}
.bottom-50{bottom: 50%;}
.bottom-60{bottom: 60%;}
.bottom-70{bottom: 70%;}
.bottom-80{bottom: 80%;}
.bottom-90{bottom: 90%;}
.bottom-100{bottom: 100%;}

/* bottom (px) */
.bottom-10px{bottom: 10px;}
.bottom-20px{bottom: 20px;}
.bottom-30px{bottom: 30px;}
.bottom-40px{bottom: 40px;}
.bottom-50px{bottom: 50px;}
.bottom-60px{bottom: 60px;}
.bottom-70px{bottom: 70px;}
.bottom-80px{bottom: 80px;}
.bottom-90px{bottom: 90px;}
.bottom-100px{bottom: 100px;}

/* top negative (px) */
.top-min-10{top:-10px;}
.top-min-20{top:-20px;}
.top-min-30{top:-30px;}
.top-min-40{top:-40px;}
.top-min-50{top:-50px;}
.top-min-60{top:-60px;}
.top-min-70{top:-70px;}
.top-min-80{top:-80px;}
.top-min-90{top:-90px;}
.top-min-100{top:-100px;}

/* bottom negative (px) */
.bottom-min-10{bottom:-10px;}
.bottom-min-20{bottom:-20px;}
.bottom-min-30{bottom:-30px;}
.bottom-min-40{bottom:-40px;}
.bottom-min-50{bottom:-50px;}
.bottom-min-60{bottom:-60px;}
.bottom-min-70{bottom:-70px;}
.bottom-min-80{bottom:-80px;}
.bottom-min-90{bottom:-90px;}
.bottom-min-100{bottom:-100px;}

/* left pos (percent) */
.start-10{left: 10%;}
.start-20{left: 20%;}
.start-30{left: 30%;}
.start-40{left: 40%;}
.start-50{left: 50%;}
.start-60{left: 60%;}
.start-70{left: 70%;}
.start-80{left: 80%;}
.start-90{left: 90%;}
.start-100{left: 100%;}

/* left pos (px) */
.start-10px{left: 10px;}
.start-20px{left: 20px;}
.start-30px{left: 30px;}
.start-40px{left: 40px;}
.start-50px{left: 50px;}
.start-60px{left: 60px;}
.start-70px{left: 70px;}
.start-80px{left: 80px;}
.start-90px{left: 90px;}
.start-100px{left: 100px;}

/* right  pos (percent) */
.end-10{right: 10%;}
.end-20{right: 20%;}
.end-30{right: 30%;}
.end-40{right: 40%;}
.end-50{right: 50%;}
.end-60{right: 60%;}
.end-70{right: 70%;}
.end-80{right: 80%;}
.end-90{right: 90%;}
.end-100{right: 100%;}

/* right  pos (px) */
.end-10px{right: 10px;}
.end-20px{right: 20px;}
.end-30px{right: 30px;}
.end-40px{right: 40px;}
.end-50px{right: 50px;}
.end-60px{right: 60px;}
.end-70px{right: 70px;}
.end-80px{right: 80px;}
.end-90px{right: 90px;}
.end-100px{right: 100px;}

/* margin-left (percent) */
.ms-10{margin-left: 10%;}
.ms-20{margin-left: 20%;}
.ms-30{margin-left: 30%;}
.ms-40{margin-left: 40%;}
.ms-50{margin-left: 50%;}
.ms-60{margin-left: 60%;}
.ms-70{margin-left: 70%;}
.ms-80{margin-left: 80%;}
.ms-90{margin-left: 90%;}
.ms-100{margin-left: 100%;}


/* margin-left (px) */
.ms-10px{margin-left: 10px;}
.ms-20px{margin-left: 20px;}
.ms-30px{margin-left: 30px;}
.ms-40px{margin-left: 40px;}
.ms-50px{margin-left: 50px;}
.ms-60px{margin-left: 60px;}
.ms-70px{margin-left: 70px;}
.ms-80px{margin-left: 80px;}
.ms-90px{margin-left: 90px;}
.ms-100px{margin-left: 100px;}


/* margin top (px) */
.mt-10{margin-top:10px;}
.mt-20{margin-top:20px;}
.mt-30{margin-top:30px;}
.mt-40{margin-top:40px;}
.mt-50{margin-top:50px;}
.mt-60{margin-top:60px;}
.mt-70{margin-top:70px;}
.mt-80{margin-top:80px;}
.mt-90{margin-top:90px;}
.mt-100{margin-top:100px;}

/* margin top negative (px) */
.mt-min-10{margin-top:-10px;}
.mt-min-20{margin-top:-20px;}
.mt-min-30{margin-top:-30px;}
.mt-min-40{margin-top:-40px;}
.mt-min-50{margin-top:-50px;}
.mt-min-60{margin-top:-60px;}
.mt-min-70{margin-top:-70px;}
.mt-min-80{margin-top:-80px;}
.mt-min-90{margin-top:-90px;}
.mt-min-100{margin-top:-100px;}

/* margin bottom (px) */
.mb-10{margin-bottom:10px;}
.mb-20{margin-bottom:20px;}
.mb-30{margin-bottom:30px;}
.mb-40{margin-bottom:40px;}
.mb-50{margin-bottom:50px;}
.mb-60{margin-bottom:60px;}
.mb-70{margin-bottom:70px;}
.mb-80{margin-bottom:80px;}
.mb-90{margin-bottom:90px;}
.mb-100{margin-bottom:100px;}

/* margin bottom negative (px) */
.mb-min-10{margin-bottom:-10px;}
.mb-min-20{margin-bottom:-20px;}
.mb-min-30{margin-bottom:-30px;}
.mb-min-40{margin-bottom:-40px;}
.mb-min-50{margin-bottom:-50px;}
.mb-min-60{margin-bottom:-60px;}
.mb-min-70{margin-bottom:-70px;}
.mb-min-80{margin-bottom:-80px;}
.mb-min-90{margin-bottom:-90px;}
.mb-min-100{margin-bottom:-100px;}

/* margin left negative (px) */
.ms-min-10{margin-left:-10px;}
.ms-min-20{margin-left:-20px;}
.ms-min-30{margin-left:-30px;}
.ms-min-40{margin-left:-40px;}
.ms-min-50{margin-left:-50px;}
.ms-min-60{margin-left:-60px;}
.ms-min-70{margin-left:-70px;}
.ms-min-80{margin-left:-80px;}
.ms-min-90{margin-left:-90px;}
.ms-min-100{margin-left:-10p0x;}

/* margin top negative (px) */
.me-min-10{margin-right:-10px;}
.me-min-20{margin-right:-20px;}
.me-min-30{margin-right:-30px;}
.me-min-40{margin-right:-40px;}
.me-min-50{margin-right:-50px;}
.me-min-60{margin-right:-60px;}
.me-min-70{margin-right:-70px;}
.me-min-80{margin-right:-80px;}
.me-min-90{margin-right:-90px;}
.me-min-100{margin-right:-100px;}

/* font-size (px) */
.fs-12{font-size: 12px;}
.fs-14{font-size: 14px;}
.fs-15{font-size: 15px;}
.fs-16{font-size: 16px;}
.fs-18{font-size: 18px;}
.fs-20{font-size: 20px;}
.fs-24{font-size: 24px;}
.fs-28{font-size: 28px;}
.fs-30{font-size: 30px;}
.fs-32{font-size: 32px;}
.fs-36{font-size: 36px;}
.fs-40{font-size: 40px;}
.fs-48{font-size: 48px;}
.fs-56{font-size: 56px;}
.fs-60{font-size: 60px;}
.fs-64{font-size: 64px;}
.fs-72{font-size: 72px;}
.fs-84{font-size: 84px;}
.fs-96{font-size: 96px;}
.fs-120{font-size: 120px;}
.fs-150{font-size: 150px;}
.fs-200{font-size: 200px;}


/* font-weight */
.fw-100{font-weight: 100;}
.fw-200{font-weight: 200;}
.fw-400{font-weight: 300;}
.fw-400{font-weight: 400;}
.fw-500{font-weight: 500;}
.fw-600{font-weight: 600;}
.fw-bold{font-weight: bold;}

/* margin left (px) */
.ms-10{margin-left: 10%;}
.ms-20{margin-left: 20%;}
.ms-30{margin-left: 30%;}
.ms-40{margin-left: 40%;}
.ms-50{margin-left: 50%;}
.ms-60{margin-left: 60%;}
.ms-70{margin-left: 70%;}
.ms-80{margin-left: 80%;}
.ms-90{margin-left: 90%;}
.ms-100{margin-left: 100%;}

/* padding (px) */
.p-10{padding: 10px;}
.p-15{padding: 15px;}
.p-20{padding: 20px;}
.p-30{padding: 30px;}
.p-40{padding: 40px;}
.p-50{padding: 50px;}
.p-60{padding: 60px;}
.p-70{padding: 70px;}
.p-80{padding: 80px;}
.p-90{padding: 90px;}
.p-100{padding: 100px;}

/* padding bottom (px) */
.pb-10{padding-bottom: 10px;}
.pb-20{padding-bottom: 20px;}
.pb-30{padding-bottom: 30px;}
.pb-40{padding-bottom: 40px;}
.pb-50{padding-bottom: 50px;}
.pb-60{padding-bottom: 60px;}
.pb-70{padding-bottom: 70px;}
.pb-80{padding-bottom: 80px;}
.pb-90{padding-bottom: 90px;}
.pb-100{padding-bottom: 100px;}

/* padding top (px) */
.pt-10{padding-top: 10px;}
.pt-20{padding-top: 20px;}
.pt-30{padding-top: 30px;}
.pt-40{padding-top: 40px;}
.pt-50{padding-top: 50px;}
.pt-60{padding-top: 60px;}
.pt-70{padding-top: 70px;}
.pt-80{padding-top: 80px;}
.pt-90{padding-top: 90px;}
.pt-100{padding-top: 100px;}
.pt-110{padding-top: 110px;}
.pt-120{padding-top: 120px;}

/* padding left (px) */
.ps-10{padding-left: 10px;}
.ps-20{padding-left: 20px;}
.ps-30{padding-left: 30px;}
.ps-40{padding-left: 40px;}
.ps-50{padding-left: 50px;}
.ps-60{padding-left: 60px;}
.ps-70{padding-left: 70px;}
.ps-80{padding-left: 80px;}
.ps-90{padding-left: 90px;}
.ps-100{padding-left: 100px;}

/* border white */
.border-white-1{border: solid 1px white;}
.border-white-2{border: solid 2px white;}
.border-white-3{border: solid 3px white;}
.border-white-4{border: solid 4px white;}
.border-white-5{border: solid 5px white;}
.border-white-6{border: solid 6px white;}
.dashed-1-white{border: dashed 1px white;}

/* border bottom white with transparency */
.border-bottom-white-trans-1{ border-bottom: solid 1px rgba(255, 255, 255, .1); }
.border-bottom-white-trans-2{ border-bottom: solid 1px rgba(255, 255, 255, .2); }
.border-bottom-white-trans-3{ border-bottom: solid 1px rgba(255, 255, 255, .3); }
.border-bottom-white-trans-4{ border-bottom: solid 1px rgba(255, 255, 255, .4); }
.border-bottom-white-trans-5{ border-bottom: solid 1px rgba(255, 255, 255, .5); }
.border-bottom-white-trans-6{ border-bottom: solid 1px rgba(255, 255, 255, .6); }
.border-bottom-white-trans-7{ border-bottom: solid 1px rgba(255, 255, 255, .7); }
.border-bottom-white-trans-8{ border-bottom: solid 1px rgba(255, 255, 255, .8); }
.border-bottom-white-trans-9{ border-bottom: solid 1px rgba(255, 255, 255, .9); }

/* border bottom grey */
.border-bottom-1-grey{border-bottom: solid 1px #dddddd;}

/* border bottom dark */
.border-bottom-1-dark{border-bottom: solid 1px var(--bg-dark-1);}

/* border bottom dark */
.border-bottom-1-light{border-bottom: solid 1px rgba(255,255,255,.5);}

/* hover effects */
.hover:hover .hover-scale-1-2{transform: scale(1.2);}
.hover-scale-in-1{transform: scale(1.1); }
.hover:hover .hover-scale-in-1{transform: scale(1); }
.hover-scale-in-3{transform: scale(3); }
.hover:hover .hover-scale-in-3{transform: scale(1); }
.hover-op-0{ opacity:1 }
.hover:hover .hover-op-0{ opacity:0 }
.hover-op-1,.hover-op-05{ opacity:0 }
.hover:hover .hover-op-1{ opacity:1 }
.hover:hover .hover-op-05{ opacity:.5 }
.hover:hover .hover-scale-1-1{transform: scale(1.1);}
.hover:hover .hover-scale-1-2{transform: scale(1.2);}
.hover:hover .hover-scale-in-3{transform: scale(1); opacity: 1}
.hover:hover .hover-bg-color{background: var(--primary-color) !important;}
.hover:hover .hover-bg-color-2{background: var(--primary-color) !important;}
.hover:hover .hover-bg-dark{background: var(--heading-font-color)}
.hover-mh-60{max-height: 0px; overflow: hidden;}
.hover:hover .hover-mh-60{max-height: 60px;}
.hover:hover .hover-top-0{top:0;}
.hover:hover .hover-pb-40{padding-bottom: 40px;}
.hover-mt-40{margin-top: 40px;}
.hover:hover .hover-mt-40{margin-top: 0px;}
.hover:hover .hover-mt-0{margin-top: 0;}
.hover:hover .hover-jello{animation-name: jello; animation-duration: 1.5s;}
.hover:hover .hover-text-light *{color: #ffffff}
.hover:hover .hover-jello{animation-name: jello; animation-duration: 1.5s;}
.hover:hover .hover-bounce{animation-name: bounce; animation-duration: 1.5s;}

.inline-block{display: inline-block;}

.infinite{ animation-iteration-count: infinite;}

/* opacity */
.op-1{ opacity:.1; }
.op-2{ opacity:.2; }
.op-3{ opacity:.3; }
.op-4{ opacity:.4; }
.op-5{ opacity:.5; }
.op-6{ opacity:.6; }
.op-7{ opacity:.7; }
.op-8{ opacity:.8; }
.op-9{ opacity:.9; }

/* line height em */
.lh-1{line-height: 1em;}
.lh-1-1{line-height: 1.1em;}
.lh-1-2{line-height: 1.2em;}
.lh-1-3{line-height: 1.3em;}
.lh-1-4{line-height: 1.4em;}
.lh-1-5{line-height: 1.5em;}
.lh-1-6{line-height: 1.6em;}
.lh-1-7{line-height: 1.7em;}
.lh-1-8{line-height: 1.8em;}

/* min height (px) */
.mh-80{min-height:80px;}
.mh-100{min-height:100px;}
.mh-200{min-height:200px;}
.mh-300{min-height:300px;}
.mh-400{min-height:400px;}
.mh-500{min-height:500px;}
.mh-600{min-height:600px;}
.mh-700{min-height:700px;}
.mh-800{min-height:800px;}
.mh-900{min-height:900px;}
.mh-1000{min-height:1000px;}

/* background dark */
.bg-dark-10{background: rgba(var(--bg-dark-1-rgb),.1)}
.bg-dark-20{background: rgba(var(--bg-dark-1-rgb),.2)}
.bg-dark-30{background: rgba(var(--bg-dark-1-rgb),.3)}
.bg-dark-40{background: rgba(var(--bg-dark-1-rgb),.4)}
.bg-dark-50{background: rgba(var(--bg-dark-1-rgb),.5)}

/* overlay-black */
.overlay-dark-1{background: rgba(var(--bg-dark-1-rgb), .1)}
.overlay-dark-2{background: rgba(var(--bg-dark-1-rgb), .2)}
.overlay-dark-3{background: rgba(var(--bg-dark-1-rgb), .3)}
.overlay-dark-4{background: rgba(var(--bg-dark-1-rgb), .4)}
.overlay-dark-5{background: rgba(var(--bg-dark-1-rgb), .5)}
.overlay-dark-6{background: rgba(var(--bg-dark-1-rgb), .6)}
.overlay-dark-7{background: rgba(var(--bg-dark-1-rgb), .7)}
.overlay-dark-8{background: rgba(var(--bg-dark-1-rgb), .8)}
.overlay-dark-9{background: rgba(var(--bg-dark-1-rgb), .9)}

/* overlay-black */
.overlay-black-1{background: rgba(0, 0, 0, .1)}
.overlay-black-2{background: rgba(0, 0, 0, .2)}
.overlay-black-3{background: rgba(0, 0, 0, .3)}
.overlay-black-4{background: rgba(0, 0, 0, .4)}
.overlay-black-5{background: rgba(0, 0, 0, .5)}
.overlay-black-6{background: rgba(0, 0, 0, .6)}
.overlay-black-7{background: rgba(0, 0, 0, .7)}
.overlay-black-8{background: rgba(0, 0, 0, .8)}
.overlay-black-9{background: rgba(0, 0, 0, .9)}

/* overlay-white */
.overlay-white-1{background: rgba(255, 255, 255, .1)}
.overlay-white-2{background: rgba(255, 255, 255, .2)}
.overlay-white-3{background: rgba(255, 255, 255, .3)}
.overlay-white-4{background: rgba(255, 255, 255, .4)}
.overlay-white-5{background: rgba(255, 255, 255, .5)}
.overlay-white-6{background: rgba(255, 255, 255, .6)}
.overlay-white-7{background: rgba(255, 255, 255, .7)}
.overlay-white-8{background: rgba(255, 255, 255, .8)}
.overlay-white-9{background: rgba(255, 255, 255, .9)}

/* positions */
.absolute,.abs { position: absolute;}
.abs-center { left: 50%; transform: translateX(-50%);}
.abs-middle{ top: 50%;  transform: translateY(-50%);}
.abs-centered{ position: absolute; top: 50%; left: 50%;transform: translate(-50%, -50%);}
.relative,.position-relative { position: relative; }

/* z-index */
.z-1{ z-index:1 }
.z-2{ z-index:2 }
.z-3{ z-index:3 }
.z-4{ z-index:4 }
.z-1000 { z-index: 1000;}

.auto-height{height: 100%;padding-top: 100%;}

.rounded-top-60{
  border-radius:60px 60px 0 0;
  -moz-border-radius:60px 60px 0 0;
  -webkit-border-radius:60px 60px 0 0;
}

.rounded-bottom-60{
  border-radius:0 0 60px 60px;
  -moz-border-radius:0 0 60px 60px;
  -webkit-border-radius:0 0 60px 60px;
}

.rounded-bottom-end-60{
  border-radius:0 0 60px 0;
  -moz-border-radius:0 0 60px 0;
  -webkit-border-radius:0 0 60px 0;
}

.rounded-bottom-start-60{
  border-radius:0 0 0 60px;
  -moz-border-radius:0 0 0 60px;
  -webkit-border-radius:0 0 0 60px;
}

.rounded-top-start-60{
  border-radius:60px 0 0 0;
  -moz-border-radius:60px 0 0 0;
  -webkit-border-radius:60px 0 0 0;
}

.border-1-black{border: solid 1px black;}

.border-1-color{border: solid 1px var(--primary-color);}

.bg-color-op-1{background: rgba(var(--primary-color-rgb), .1)}
.bg-color-op-2{background: rgba(var(--primary-color-rgb), .2)}
.bg-color-op-3{background: rgba(var(--primary-color-rgb), .3)}
.bg-color-op-4{background: rgba(var(--primary-color-rgb), .4)}
.bg-color-op-5{background: rgba(var(--primary-color-rgb), .5)}
.bg-color-op-6{background: rgba(var(--primary-color-rgb), .6)}
.bg-color-op-7{background: rgba(var(--primary-color-rgb), .7)}
.bg-color-op-8{background: rgba(var(--primary-color-rgb), .8)}

.ratio-1-1{
  width: 100%;
  position: relative;
}
.ratio-1-1:before{
  content: "";
   display: block;
   padding-top: 100%;
}

.rtl .rtl-hide{
  display: none;
}

.rtl-show{
  display: none;
}

.rtl .rtl-show{
  display: block;
}

/* declass end */

/* shop */

.de__pcard h3{
  font-weight: bold;
  margin-top: 5px;
  margin-bottom: 0px;
  font-size: 17px;
}

.atr__images{
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.atr__images img{
  width: 100%;
  height: auto;
  background: var(--bg-dark-2);
  padding: 20px;
  border-radius:var(--rounded-1);
  -moz-border-radius:var(--rounded-1);
  -webkit-border-radius:var(--rounded-1);
}

.atr__image-hover{
  opacity: 0;
  z-index: 1;
  position: absolute;
  left: 0;
}

.atr__images .atr__image-hover.full{
  padding: 0;
}

.atr__images:hover .atr__image-hover{
  opacity: 1;
  -o-transition:.8s;
  -ms-transition:.8s;
  -moz-transition:.8s;
  -webkit-transition:.8s;
  transition:.8s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.atr__colors{
  margin-top: 10px;
}

.atr__colors div{
  border: solid 1px rgba(51, 51, 51, 0);
  display: inline-block;
  margin: 0 2px;border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
}

.atr__colors span{
  display: block;
  background: #333333;
  width: 20px;
  height: 20px;
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
}

.atr__colors div.active{
  border: solid 1px #ffffff;
  padding: 3px;
}

.atr__colors div.active span{
  width: 15px;
  height: 15px;
}

.atr__opt{  
  text-align: center;
}

.atr__opt div{
  display: inline-block;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  margin: 2px;
  border:solid 1px var(--secondary-color);
  border-radius:5px;
  -moz-border-radius:5px;
  -webkit-border-radius:5px;
  line-height: 1em;
  width: 32px;
}

.atr__opt div.active{
  background: var(--secondary-color);
  color:#ffffff;
}

.de__pcard .atr__extra-menu{
  z-index: 1;
  text-align: center;
  position: absolute;
  width: 100%;
  transform: scale(0);
  bottom: 20px;
}

.de__pcard .atr__extra-menu div,
.de__pcard .atr__extra-menu a{
  display: inline-block;
  cursor: pointer;
  margin: 0 2px;
}

.de__pcard .atr__extra-menu div i,
.de__pcard .atr__extra-menu a i{
  color:#ffffff;
  background: var(--bg-dark-1);
  font-size: 18px;
  padding: 10px;
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
}

.de__pcard .atr__extra-menu div.active i{
  background: var(--primary-color);
  color: #ffffff;
}


.de__pcard .atr__extra-menu div:hover:not(.active) i{
  background: #333333;
  color: #ffffff;
}

.de__pcard .atr__images:hover .atr__extra-menu{
  transform: scale(1);
}

.de__pcard .atr__promo{
  position: absolute;
  z-index: 1;
  display: inline-block;
  background: var(--primary-color);
  color:#ffffff;
  padding: 3px 8px;
  line-height: 1em;
  margin: 10px 0 0 10px;
  font-weight: bold;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

#de_notif{
  opacity: 0;
  font-weight: bold;
  background: #ffffff;
  color: #000000;
  padding: 5px 10px;
  position: fixed;
  left: 50%;
  bottom: -50px;
  transform: translate(-50%, 0);
  z-index: 100;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
}

#de_notif.active{
  opacity: 1;
  bottom: 40px;
}

/* shop end */


/* cart begin */

.de__cart-item{
  display: flex;
  margin-bottom: 30px;
}
.de__cart-item img{
  position: absolute;
  width: 64px;
  margin: 0 15px;
  background: var(--bg-light);
  padding: 5px;
  border-radius:5px;
  -moz-border-radius:5px;
  -webkit-border-radius:5px;
}
.de__cart-item h4{
  margin-bottom: 0;
  font-size: 18px;
}
.d-checkbox__input {
  display: none;
}
.d-checkbox__input:checked + .d-checkbox__label:after {
  opacity: 1;
}
.d-checkbox__label {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
  cursor: pointer;
}
.d-checkbox__label:before {
  content: "";
  width: 16px;
  height: 16px;
  border: 2px solid var(--secondary-color);;
  box-sizing: border-box;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
.d-checkbox__label:after {
  font-family: "FontAwesome";
  content: "\f00c";
  background: var(--secondary-color);
  color:#ffffff;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 2px;
  overflow: hidden;
  text-align: center;
  opacity: 0;
  transition: 0.2s opacity;
}
.de__cart-item .d-info{
  display: inline-flex; 
  margin-left: 95px
}
.de__cart-item .d-price{
  font-size: 18px;
}
.de__cart-item .de-number .d-plus,
.de__cart-item .de-number .d-minus{
  padding-top:5px;
}
.de__cart-item .de-number input{
  font-weight: bold;
  font-size: 16px;
}


#extra-wrap .de__cart-item .d-info{
  max-width: 150px;
  text-overflow: ellipsis;
  white-space: nowrap;
    overflow: hidden;

}
#extra-wrap .de__cart-item h4,
#extra-wrap .de__cart-item .d-price{
  font-size: 14px;
}

/* cart end */

/* ecommerce icons begin */
.de-icon-counter{
  cursor: pointer;
  position: relative;
  padding: 10px;
  padding-right: 15px;
}
.de-icon-counter img{
  width: 24px;
}
.de-icon-counter .d-counter{
  background: var(--primary-color);
  font-size: 12px;
  width: 24px;
  height: 24px;
  color:#ffffff;
  position: absolute;
  top: 0;
  right: 0;
  padding-top: 6px;
  line-height: 1;
  text-align: center;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
}

.de-quick-search{
  color: #ffffff;
  background: none;
  outline: none;
  border:solid 1px rgba(255, 255, 255, .5);
  padding: 3px 10px;
  max-width: 150px;
}
/* ecommerce icons end */

/* radio__button begin */
.radio__button input[type="radio"]{
  visibility: hidden;
  height: 0;
  width: 0;
}

.radio__button label {
  font-size: ;
  color:var(--secondary-color);
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
  border:solid 1px var(--primary-color);
  color: var(--primary-color);
  line-height: 1em;
  padding: 2px 10px;
  border-radius: 3px;
  opacity: .75;
}
.radio__button input[type="radio"]:checked + label{
  border:solid 2px var(--primary-color);
  font-weight: bold;
  opacity: 1;
}


/* radio__button end */


/* owl sync begin */
#sync1 .item {
  background: var(--bg-dark-2);
  padding: 40px;
  margin: 5px;
  color: #FFF;
  -webkit-border-radius: var(--rounded-1);
  -moz-border-radius: var(--rounded-1);
  border-radius: var(--rounded-1);
  text-align: center;
}

#sync2 .item {
  background: var(--bg-dark-2);
  margin: 5px;
  color: #FFF;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
#sync2 .item h1 {
  font-size: 18px;
}
#sync2 .current .item {
  background: var(--secondary-color);
}

.owl-theme .owl-nav {
  /*default owl-theme theme reset .disabled:hover links */
}
.owl-theme .owl-nav [class*=owl-] {
  transition: all 0.3s ease;
}
.owl-theme .owl-nav [class*=owl-].disabled:hover {
  background-color: #D6D6D6;
}

#sync1.owl-theme {
  position: relative;
}
#sync1.owl-theme .owl-next, #sync1.owl-theme .owl-prev {
  width: 22px;
  height: 40px;
  margin-top: -20px;
  position: absolute;
  top: 50%;
}
#sync1.owl-theme .owl-prev {
  left: 10px;
}
#sync1.owl-theme .owl-next {
  right: 10px;
}
/* owl sync end */

/* price range slider */
.price-input {
  width: 100%;
  display: flex;
  margin: 0 0 15px;
}
.price-input .field {
  display: flex;
  width: 100%;
  align-items: center;
}
.field input {
  color: #ffffff;
  width: 60px;
  outline: none;
  font-size: 16px;
  margin-left: 12px;
  border-radius: 3px;
  border: none;
  font-weight: bold;
  -moz-appearance: textfield;
  background: none;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.price-input .separator {
  width: 130px;
  display: flex;
  font-size: 19px;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  margin-left: 10px;
}
.slider {
  height: 5px;
  position: relative;
  background: var(--bg-dark-3);
  border-radius: 5px;
}
.slider .progress {
  height: 100%;
  left: 0%;
  right: 0%;
  position: absolute;
  border-radius: 5px;
  background: var(--primary-color);
}
.range-input {
  position: relative;
  margin-bottom: 10px;
}
.range-input input {
  position: absolute;
  width: 100%;
  height: 5px;
  top: -5px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}
input[type="range"]::-webkit-slider-thumb {
  height: 17px;
  width: 17px;
  border-radius: 50%;
  background: var(--primary-color);
  pointer-events: auto;
  -webkit-appearance: none;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
}
input[type="range"]::-moz-range-thumb {
  height: 17px;
  width: 17px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  pointer-events: auto;
  -moz-appearance: none;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
}
/* range slider end */

/* custom tabs */
.de-tab .d-tab-nav {
  padding: 0;
  list-style: none;
  position: relative;
}
.de-tab .d-tab-nav .active-tab {
  border-bottom: none;
  font-weight: 500;
  position: relative;
  color: #ffffff;
  background: var(--primary-color);
}
.de-tab .d-tab-nav li {
  display: block;
  cursor: pointer;
  padding: 10px;
}
.de-tab .d-tab-content {
  padding: 0;
  list-style: none;
}
.de-tab .d-tab-content > li {
  display: none;
}

.de-tab .d-tab-nav li{
  display: inline-block;
  padding: 8px 20px;
}

.de-tab.pill .d-tab-nav{
  background: rgba(var(--primary-color-rgb),.2);
}

.de-tab.pill .d-tab-nav{  
  display: flex;
  justify-content: space-around;
  border: none;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
  overflow: hidden;
}
.de-tab.pill .d-tab-nav li{
  font-weight: 500;
  color:#000000;
  text-align: center;
  flex-grow:1;
}
.de-tab.pill .d-tab-nav .active-tab{
  background: var(--primary-color);
  color:#ffffff;
  border:none;
}

.de-tab.plain .d-tab-nav li{
  font-weight: 500;
  color:#000000;
  text-align: center;
  padding: 0;
  margin: 0;
  opacity: .4;
}
.de-tab.plain .d-tab-nav .active-tab{
  background: none;
  font-weight: bold;
  opacity: 1;
}


@media only screen and (max-width: 767px) {
  .d-tab-nav,
  .d-tab-nav li{
    display: block !important;
  }
}

/* custom tabs end */

/* de-dot */


.de-dot {
    position: absolute;
    z-index: 10;
    box-sizing: content-box;
    display: inline-block;
    width: 32px;
    height: 44px;
    border-radius: 50%;
    padding: 18px 20px 18px 28px;
}

.de-dot:before {
    content: "";
    position: absolute;
    z-index: 0;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 15px;
    height: 15px;
    background: orange;
    border-radius: 50%;
    animation: dot-pulse 1500ms ease-out infinite;
}

.de-dot:after {
    content: "";
    position: absolute;
    z-index: 1;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 15px;
    height: 15px;
    background: orange;
    border-radius: 50%;
    transition: all 200ms;
}


@keyframes dot-pulse {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }

    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(2.2);
        opacity: 0;
    }
}


.de-dot .d-content{
  transform: scale(0);
  transform-origin: top left;
  background: rgba(var(--bg-dark-1-rgb),.8);
  opacity: 0;
  overflow: hidden;
  margin-left: 10px;
  margin-top:20px;
  width: 210px;
  border-radius:10px;
  -moz-border-radius:10px;
  -webkit-border-radius:10px;
  padding: 20px 25px;
  font-size: 15px;
  line-height: 1.6em;
}

.de-dot:hover .d-content{
  transform: scale(1);
  opacity: 1;
}

.de-dot:hover:after{
 background: #ffffff;  
}

.de-dot:hover:before{
 background: #ffffff;  
}


.ss-wrap{
  position: relative;
  width: 120%;
  margin-left: -10%;
  margin-bottom: -100px;
}

.ss-wrap img{
  width: 25%;
  box-shadow: 0 0.25rem 0.5rem rgb(0 0 0 / 15%), 0 1.5rem 2.2rem rgb(0 0 0 / 25%);
  border-radius:10px;
  -moz-border-radius:10px;
  -webkit-border-radius:10px;
}

.ss-wrap .img-l1{
  top:10%;
  left: 20%;
  z-index: 3;
}

.ss-wrap .img-l2{
  top:20%;
  left: 0%;
}


.ss-wrap .img-r1{
  top:10%;
  right: 20%;
  z-index: 3;
}

.ss-wrap .img-r2{
  top:20%;
  right: 0%;
}

.body-font{
  font-family: var(--body-font);
}

/* de-vertical-nav begin */

.de-vertical-nav a{
  background: var(--bg-grey);
  border-radius:2px;
  -moz-border-radius:2px;
  -webkit-border-radius:2px;
  display: block;
  padding: 40px;
  position: relative;
}

.de-vertical-nav img{
  position: absolute;
  width: 40px;
  top: 50%;  transform: translateY(-50%);
}

/* de-vertical-nav end */

.menu-simple {
  display: inline;
  list-style: none;
  margin: 0;
  padding: 0;
  font-weight: 500;
}

.menu-simple li {
  display: inline;
  margin: 0 15px;
}


/* timeline begin */
.de-timeline .d-item{
  display: flex;
  justify-content: space-between;
  position: relative;
}

.de-timeline .d-item{
  content: "";
  border-left: solid 1px #555;
  margin-left: 20px;
  padding-bottom: 30px;
}

.de-timeline .d-item  .d-icon{
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
  position: absolute;
  width: 28px;
  height: 28px;
  text-align: center;
  left: -15px;
  color:var(--heading-font-color);
  background: var(--primary-color);
}

.de-timeline .d-item.active .d-icon{
  animation: pulsing 2s infinite;
  transition: all 0.2s;
}

.de-timeline .d-item .d-text{
  max-width: 80%;
  padding-left: 40px;
}

.de-timeline .d-item .d-text h4{
  margin-bottom: 5px;
}

.de-timeline .d-item:not(.active){
  opacity: .25;
}

@keyframes pulsing {
  from {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb),1);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(var(--primary-color-rgb),0);
  }
  to {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb),0);
  }
}

/* de-timeline end */

/* timeline-s2 begin */
.de-timeline-s2 .d-item{
  display: flex;
  justify-content: space-between;
  position: relative;
}

.de-timeline-s2 .d-item{
  content: "";
  border-left: solid 1px #555;
  margin-left: 20px;
  padding-bottom: 30px;
}

.de-timeline-s2.color .d-item{
  border-left-color: var(--primary-color);
}


.de-timeline-s2 .d-item.no-border{
  border: none;
}

.de-timeline-s2 .d-item  .d-icon{
  font-weight: bold;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
  position: absolute;
  width: 40px;
  height: 40px;
  padding-top: 5px;
  text-align: center;
  left: -20px;
  color:#ffffff;
  background: var(--bg-dark-1);
}

.de-timeline-s2.color .d-item  .d-icon{
  background: var(--primary-color);
}

.de-timeline-s2 .d-item.active .d-icon{
  animation: pulsing 2s infinite;
  transition: all 0.2s;
}

.de-timeline-s2 .d-item .d-text{
  padding-left: 40px;
}

.de-timeline-s2 .d-item .d-text h4{
  margin-bottom: 5px;
}

@keyframes pulsing {
  from {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb),1);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(var(--primary-color-rgb),0);
  }
  to {
    box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb),0);
  }
}

/* de-timeline end */


/* swiperjs */

.swiper{
  background: var(--bg-dark-1);  
}

.swiper,
.sw-overlay{
    position: absolute;
    width: 100%;
    height: 100%;
    left:0;
    top: 0;
}

.swiper-slide{
    position: relative;
    background: var(--bg-dark-1);
}

.sw-caption{
    z-index: 1;
    position: absolute;
    width: 100%;
    top: 50%;
    transform: translateY(-50%);
    letter-spacing: -.5;
}

.sw-text-wrapper.border-left{
  border-left: solid 1px rgba(0, 0, 0, .25);
  padding-left: 40px;
}

.sw-text-wrapper.border-left.light{
  border-left: solid 1px rgba(255, 255, 255, .25);
  padding-left: 40px;
}

.slider-title .underline{
  position: relative;
}
.slider-title .underline:before{
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px;
  background: var(--primary-color);
}
h1.slider-title{
  font-size: 64px;
  font-weight: 500;
  margin-bottom: 20px;
  line-height: 1.1em;
  letter-spacing: -2px;
}
h2.slider-title{
  font-size: 64px;
  letter-spacing: -3px;
}
h3.slider-teaser{
  font-size: 24px;
}
p.slider-teaser{
  font-size: 18px;
  line-height: 1.6em;
  font-weight: 400;
} 

.sw-overlay{
  background: -webkit-linear-gradient(0deg,rgba(var(--bg-dark-1-rgb), .75) 0%, rgba(var(--bg-dark-1-rgb), .75) 50%);
  background: -moz-linear-gradient(0deg,rgba(var(--bg-dark-1-rgb), .75) 0%, rgba(var(--bg-dark-1-rgb), .75) 50%);
  background: linear-gradient(0deg,rgba(var(--bg-dark-1-rgb), .75) 0%, rgba(var(--bg-dark-1-rgb), .75) 50%);
}

.swiper-inner{
  display: block;
  position: absolute;
  top:0;
  width: 100%;
  height: 100%;
}

.swiper-pagination{
  width: 100px;
  font-size: 20px;
  text-align: right;
  position: absolute;
  left: auto;
  right: 40px;
  bottom: 40px;
  font-weight: 400;
}

.swiper-pagination-total{
  font-size:20px;
  opacity: .5;
}

.swiper-pagination-current{
  color:#ffffff;
  font-size: 20px;
}

.slider-light .swiper-pagination-current{
  color: var(--heading-font-color);
}

.swiper-button-next, .swiper-button-prev{
  color:#ffffff;
}

.slider-light .swiper-button-next, .slider-light .swiper-button-prev{
  color:var(--primary-color);
}

.swiper-button-next:after, .swiper-button-prev:after{
  font-size:24px;
}

.swiper-button-next{
  right: 20px;
}

.swiper-button-prev{
  left: 20px;
}

.sw-price{
  color:#ffffff;
}
.sw-price .d-starting{
  margin-bottom: 20px;
  color:rgba(255, 255, 255, .75);
}
.sw-price .d-price{
  font-size: 56px;
  font-family: var(--heading-font);
  font-weight: bold;
  margin-bottom: 10px;
}
.sw-price .d-val{
  background: -webkit-linear-gradient(0deg,#888888 0%, #ffffff 75%);
  background: -moz-linear-gradient(0deg,#888888 0%, #ffffff 75%);
  background: linear-gradient(0deg,#888888 0%, #ffffff 75%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.sw-price .d-cur, .sw-price .d-period{
  font-size: 16px;
  font-family: var(--body-font);
  color:var(--primary-color);
}
.sw-price .d-cur{
  font-weight: bold;
}


/* swiper end */


/* swiper #2 begin */

.swiper-container {
  overflow: hidden;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 480px) {
  .swiper-container {
    min-height: 320px;
  }
}
.swiper-container-wrapper {
  display: flex;
  flex-flow: column nowrap;
  height: 100vh;
  width: 100vw;
}
@media (min-width: 480px) {
  .swiper-container-wrapper {
    flex-flow: row nowrap;
  }
}

#swiper-s2 .swiper-slide {
  text-align: center;
  background-size: cover;
  background-position: center;
  background-color: var(--bg-dark-1);
  /* Center slide text vertically */
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
  /* Slide content */
}
.swiper-slide .description,
.swiper-slide .title {
  display: block;
  opacity: 0;
  transition: 0.5s ease 0.5s;
}
.swiper-slide-active .description,
.swiper-slide-active .title {
  opacity: 1;
}
.swiper-slide-active .title {
  margin-bottom: 0.5rem;
  font-size: 24px;
  color: #000;
  transition: opacity 0.5s ease 0.5s;
}
.swiper-slide-active .description {
  font-size: 16px;
  color: #777;
  transition: opacity 0.5s ease 0.75s;
}

.sw-caption-thumb h3{
  font-size: 22px
}

.sw-caption-thumb .d-tag{
  color:#ffffff;
  font-size: 12px;
  font-weight: bold;
  background: var(--primary-color);
  padding: 2px 10px;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
}

/* swiper #2 end */


.player {
  margin: 0 auto;
  background: none;
  width: 120px;
  height: 120px;
  border: solid 2px #fff;
  position: relative; 
  text-indent:-9999px;
}

.player span {
  width: 0;
  height: 0;
  border-top: 15px solid transparent;
  border-left: 20px solid #ffffff;
  border-bottom: 15px solid transparent;
  position:absolute;
  top:37.5%;
  left:45%; 
}

.player:hover{
  background:var(--primary-color);
  cursor:pointer;
  border:none;
  box-shadow:none;
}

.player:hover span{
  transform: scale(.8);
}

.player.invert span{
  border-left: 20px solid #000;
}


.player.invert:hover span{
  border-left: 20px solid #fff;
}

.de-flex #mainmenu{
  margin: 0;
}

.col-start, .col-end {
  display: flex;
  align-items: center;
  justify-content: start;
  flex-grow: 1;
  flex-basis: 0;
}

.col-end{
  justify-content: end;
}

.col-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.col-center img{
  width: 160px;
}


.subtitle{
  position: relative;
  font-weight: 500;
  position: relative;
  display: inline-block;
  font-size: 12px;
  font-family:var(--heading-font);
  text-transform: none;
  letter-spacing: 0;
  line-height: 1.5em;
  margin-bottom: 20px;
  padding: 6px 0 3px 0;
  letter-spacing: 10px;
  text-transform: uppercase;
  border-radius:3px;
  -moz-border-radius:3px;
  -webkit-border-radius:3px;

}

.subtitle:before,
.subtitle:after{
  content: '';
  position: absolute;
  width: 50%;
  height: 2px;
  background: var(--primary-color);
  left: 0;
  bottom: 0;
}

.dark-scheme .subtitle, .text-light .subtitle{
  color:#ffffff;
}

.subtitle:after{
  bottom: auto;
  left: auto;
  top:0;
  right: 0;
}

.text-light .subtitle{
  border-color: rgba(255, 255, 255, .5);
}

@keyframes blinkingText{
  0%    { background: var(--primary-color);}
  50%   { background: rgba(var(--primary-color-rgb), 0); }
  100%    { background: var(--primary-color);}
}

.scrollbar {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 4px;
  transition: all linear 0.1s;
  min-width: 0%;
  z-index:1000;
}

.scrollbar-v {
  background: var(--primary-color);
  position: fixed;
  top: calc(50% + 60px);
  left: 43px;
  width: 2px;
  transition: all linear 0.1s;
  min-height: 0%;
  z-index:1000;
}

.scrollbar-v:before{
  content: "";
  position: absolute;
  left: 0;
  width: 2px;
  min-height: 100px;
  background: rgba(0, 0, 0, .15);
}


.scrollbar-v.dark:before{
  background: rgba(255, 255, 255, .2);
}

.float-text {
    width: 20px;
    position: fixed;
    z-index: 1002;
    margin-left: 30px;
    text-align: center;
    font-size: 12px;
    top: 50%;
    left: 16px;
    transform: translate(-50%, -50%);
    font-weight: 500;
}

.float-text a{
    color: #000000;
}

.float-text.dark a{
  color:#ffffff;
}

.float-text span{
    margin-left: -5px;
    margin-top: 20px;
    writing-mode: vertical-rl;
    letter-spacing: .75px;
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
    color: #222;
}


.float-text-right {
    width: 20px;
    position: fixed;
    z-index: 1000;
    margin-left: 30px;
    text-align: center;
    letter-spacing: 2px;
    font-size: 12px;
    top: 50%;
    right: 15px;
}

.de_social-icons a{
  color:#ffffff;
  margin:10px 0;
}


.dark-scheme .float-text span{
    color: #ffffff;
}

.triangle-bottomright-dark {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid var(--bg-dark-1);
  border-left: 60px solid transparent;
}


.triangle-bottomright-dark-half {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 30px solid var(--bg-dark-1);
  border-left: 30px solid transparent;
}

.triangle-bottomright-white {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid #ffffff;
  border-left: 60px solid transparent;
}

.triangle-bottomright-light {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid var(--bg-light);
  border-left: 60px solid transparent;
}


.triangle-bottomright-dark-2 {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid var(--bg-dark-2);
  border-left: 60px solid transparent;
}


.triangle-bottomright-dark-2-half {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 30px solid var(--bg-dark-2);
  border-left: 30px solid transparent;
}


.triangle-bottomright-color {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid var(--primary-color);
  border-left: 60px solid transparent;
}

.triangle-bottomleft-dark-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 0;
  border-bottom: 60px solid var(--bg-dark-2);
  border-right: 60px solid transparent;
}

/* de-number begin */

    .de-number{
      margin-top: 8px;
    }

    .d-minus, .d-plus{
      margin-top: -6px;
      cursor: pointer;
      color: #ffffff;
      font-size: 24px;
      width:36px;
      height:36px;
      padding:3px 5px 3px 5px;
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      -webkit-user-select: none; /* Safari */
      -ms-user-select: none; /* IE 10 and IE 11 */
      user-select: none; /* Standard syntax */
      background: var(--primary-color);
    }
    .d-minus:active, .d-plus:active{
      background: #ffffff;
      color: #000;
    }
    .de-number input{
      width: 40px;
      text-align: center;
      font-size: 20px;
      border:none;
      padding: 3px !important;
      background: none;
      color:#ffffff;
      border:solid 1px var(--primary-color);
      }
/* de-number close */

#recaptcha {
    width: 304px;
    margin: 0 auto;
}

.carousel-indicators [data-mdb-target]{
  background: none !important;
  margin: 0 10px;
}
.carousel-indicators [data-mdb-target] img {
    margin-top: 20px;
}

.d_demo_img {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 5px;
  margin-bottom: 15px;
  border-radius:12px;
  -moz-border-radius:12px;
  -webkit-border-radius:12px;

  box-shadow: 5px 20px 40px 5px rgba(0,0,0,.1);
  -webkit-box-shadow: 5px 20px 40px 5px rgba(0,0,0,.1);
  -moz-box-shadow: 5px 20px 40px 5px rgba(0,0,0,.1);
}

.d_demo_img:hover{
  border-color:rgba(var(--primary-color-rgb),1);
}

.d_demo_img .d-overlay {
  opacity: 0;
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.d_demo_img .d-overlay span {
  color: #ffffff;
  background: var(--bg-dark-1);
  padding: 5px 15px;
  font-weight: bold;
  transform: scale(1.5);
  opacity: 0;
  border-radius:6px;
  -moz-border-radius:6px;
  -webkit-border-radius:6px;
}

.d_demo_img:hover img {
  transform: scale(1.1);
}

.d_demo_img:hover .d-overlay {
  opacity: 1;
}

.d_demo_img:hover .d-overlay span {
  transform: scale(1.0);
  opacity: 1;
}

.de-hover-scale:hover{
  transform: scale(1.2);
  -webkit-box-shadow: 0 10px 30px rgb(0 19 87 / 25%);
  -moz-box-shadow: 0 10px 30px rgb(0 19 87 / 25%);
  box-shadow: 0 10px 30px rgb(0 19 87 / 25%);
}

.soft-shadow {
  box-shadow: 0 0.25rem 0.5rem rgb(0 0 0 / 5%), 0 1.5rem 2.2rem rgb(0 0 0 / 10%);
}

.rtl .owl-carousel,
.rtl .bx-wrapper {
  direction: ltr;
}

.rtl .owl-carousel .owl-item {
  direction: rtl;
}

#item-carousel-big {
  padding: 0 25px;
}

.de-flex {
  display: flex;
  justify-content: space-between;
}

.de-flex>.de-flex-col {
  display: flex;
  justify-content: center;
  align-items: center;
}

#loadmore {
  display: inline-block;
  margin-top: 10px;
}

.d-item {
  display: none;
}

.filter__l,
.filter__r {
  font-family: var(--heading-font);
}

.filter__l {
  font-weight: bold;
}

.filter__r {
  float: right;
  cursor: pointer;
}

/*Cookie Consent Begin*/
#cookieConsent {
  background-color: rgba(255, 255, 255, 0.8);
  min-height: 26px;
  font-size: 14px;
  line-height: 26px;
  font-weight: 500;
  padding: 10px 30px 10px 30px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: none;
  z-index: 9999;
}

#cookieConsent a {
  text-decoration: none;
}

#closeCookieConsent {
  float: right;
  display: inline-block;
  cursor: poInter;
  height: 20px;
  width: 20px;
  margin: -15px 0 0 0;
  font-weight: bold;
}

#closeCookieConsent:hover {
  color: #FFF;
}

#cookieConsent a.cookieConsentOK {
  color: #fff;
  display: inline-block;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  padding: 0 20px;
  cursor: poInter;
}

#cookieConsent a.cookieConsentOK:hover {
  opacity: .8;
}

/*Cookie Consent End*/

.vertical-center {
  min-height: 100%;
  /* Fallback for browsers do NOT support vh unit */
  min-height: 100vh;
  /* These two lines are counted as one :-)       */

  display: flex;
  align-items: center;
}

#purchase-now {
  position: fixed;
  bottom: 80px;
  left: 20px;
  z-index: 10000;
}

#purchase-now a {
  color: #ffffff;
  z-index: 1;
  position: absolute;
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
  background: #78A938;
  display: block;
  font-size: 24px;
  font-weight: bold;
  width: 60px;
  height: 60px;
  padding: 17px;
  text-decoration: none;
  animation: shadow-pulse 1s infinite;
}


#buy-now {
  position: fixed;
  bottom: 60px;
  left: 40px;
  z-index: 10000;
  direction: ltr !important;

}

#buy-now a {
  color: #ffffff;
  z-index: 1;
  position: absolute;
  background: #333333;
  display: block;
  font-size: 14px;
  font-weight: bold;
  padding: 10px 15px;
  text-align: center;
  width: 150px;
  line-height: 1em;
  text-decoration: none;
  animation: shadow-pulse 1s infinite;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
}

#buy-now a img{
  width: 64px;
  margin-left: 3px;
}

@keyframes shadow-pulse {
  0% {
    box-shadow: 0 0 0 0px rgba(120, 169, 56, 0.2);
  }

  100% {
    box-shadow: 0 0 0 20px rgba(120, 169, 56, 0);
  }
}

#purchase-now a span {
  font-size: 14px;
  font-weight: 500;
  left: 8px;
  position: absolute;
  top: 18px;
  width: 100px;
}

#purchase-now .pn-hover {
  position: absolute;
  font-weight: bold;
  font-size: 16px;
  background: #ffffff;
  color: #78A938;
  width: 120px;
  padding: 10px;
  padding-left: 20px;
  text-align: center;
  top: 8px;
  border-radius: 0px;
  left: 30px;
  opacity: 0;
}

#purchase-now:hover .pn-hover {
  opacity: 1;
  left: 40px;
}

.opt-1 {
  display: inline-block;
}

.opt-2 {
  display: none;
}

.border {
  border-color: #EFEFEF !important;
}

.tparrows {
  background: none !important;
}

.gray img,
img.gray {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
}

.text-light .border {
  border-color: rgba(255, 255, 255, .1) !important;
}

.owl-thumbs img {
  padding: 3px;
}

#slider-carousel .owl-thumbs {
  padding: 3px;
  margin-bottom: 30px;
  width: auto;
}

#slider-carousel .owl-thumbs button {
  opacity: 1;
  border: none !important;
  padding: 0;
  margin-right: 15px;
  margin-bottom: 20px;
  opacity: 1;
  overflow: hidden;
  opacity: .5;
}

#slider-carousel .owl-thumb-item.active {
  opacity: 1;
}

#slider-carousel button.owl-thumb-item.active {
  -webkit-box-shadow: 5px 5px 20px 0px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 5px 5px 20px 0px rgba(0, 0, 0, 0.5);
  box-shadow: 5px 5px 20px 0px rgba(0, 0, 0, 0.5);
}

#slider-carousel .owl-thumb-item img {
  background: #fff;
  padding: 0;
  width: 120px;
}

.de_tab.tab_style_4 .de_nav {
  -webkit-touch-1A1C26out: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -khtml-user-select: none;
  /* Konqueror HTML */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
                                  supported by Chrome and Opera */
}

strong {
  font-weight: bold;
}

.text-light strong {
  color: #fff;
}

.bg-white {
  background: #ffffff;
}

.bg-white-10 {
  background: rgba(255, 255, 255, .1);
}

.wh80 {
  width: 80%;
  height: 80%;
}

.border-top {
  border-top: solid 1px #e5e5e5;
}

.border-bottom {
  border-bottom: solid 1px #e5e5e5;
}

.text-light .border-bottom {
  border-bottom: solid 1px rgba(255, 255, 255, .3) !important;
}

.dark-scheme .border-bottom,
.dark-scheme header.border-bottom {
  border-bottom: solid 1px rgba(255, 255, 255, .25) !important;
}

.color-overlay {
  background: rgba(157, 130, 235, .3);
}

section .color-overlay {
  padding: 60px 0 60px 0;
}

.color-overlay-2 {
  background: rgba(26, 37, 48, .85);
}

.circle-x {
  background: #333;
  display: inline-block;
  padding: 20px;
  font-size: 40px;
  color: #fff;
  margin-top: 140px;
  width: 70px;
  height: 70px;
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
  -webkit-box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.6);
  -moz-box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.6);
  box-shadow: 8px 8px 40px 0px rgba(0, 0, 0, 0.6);
}

#navigation {
  position: fixed;
  width: 100%;
  height: 60px;
  top: 0;
  right: 20px;
  text-align: center;
  margin-bottom: 60px;
  z-index: 1000;
}

.nav-prev:before,
.nav-next:before,
.nav-exit:before {
  font-family: "FontAwesome";
  content: '\f177';
  color: #fff;
  padding: 20px;
  font-size: 24px;
  cursor: poInter;
}

.nav-exit:before {
  width: 100%;
  display: block;
}

.nav-next:before {
  content: '\f178';
  width: 100%;
  background: #555;
}

.nav-exit:before {
  content: '\f00d';
}

.de_light .nav-exit:before {
  color: #222;
}

.no-bottom {
  margin-bottom: 0;
  padding-bottom: 0;
}

p {
  margin-bottom: 20px;
}

p.intro {
  color: #555;
  font-size: 16px;
  line-height: 1.5em;
}

.bg-grey {
  background: #f6f6f6;
}

.no-bottom .col-md-12 {}

.no-bottom {
  padding-bottom: 0 !important;
}

.no-top {
  padding-top: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.nopadding {
  padding: 0;
  margin: 0;
}

#filters i {
  margin-right: 10px;
}

.bg-side {
  position: relative;
  overflow: hidden;
}

.image-slider {
  cursor: move;
}

.inner-padding {
  padding: 90px;
}

.inner-padding.pos-left {
  padding: 90px;
  padding-left: 0;
}

.rtl .inner-padding.pos-left {
  padding-left: 90px;
  padding-right: 0;
}

.no-padding,
section.no-padding {
  padding: 0;
}

.float-left {
  float: left;
}

#loader-area {
  display: none;
  background: #fff;
}

#loader-area {
  display: none;
  background: #111;
}

.loader {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 99999;
}

.page-overlay {
  display: none;
  width: 120px;
  height: 120px;
  border-radius: 0px;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10000;
  margin: auto;
}

.teaser {
  font-size: 15px;
  letter-spacing: 10px;
  text-transform: uppercase;
  line-height: 1.8em;
}

.teaser.style-2 {
  letter-spacing: 20px;
  text-transform: uppercase;
}

.teaser.style-3 {
  font-size: 14px;
}

.teaser.style-4 {
  letter-spacing: 5px;
  text-transform: uppercase;
  font-size: 14px;
}

.form-control:focus {
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
}

.d-carousel {
  position: relative;
  display: flex;
  align-items: center;
}

.d-custom-nav {
  position: absolute;
  display: flex;
  width: 100%;
  height: 100px;
  z-index: 100;
}

.d-arrow-left,
.d-arrow-right {
  display: none;
  cursor: pointer;
  position: absolute;
  margin-top: -10px;
  z-index: 100;
  width: 30px;
  height: 60px;
  background-color: rgba(255, 255, 255, 1.0);
}

.d-arrow-left {
  left: 0px;
  border-top-right-radius: 110px;
  border-bottom-right-radius: 110px;
}

.d-arrow-right {
  right: 0px;
  border-top-left-radius: 110px;
  border-bottom-left-radius: 110px;
}

.d-arrow-left i,
.d-arrow-right i {
  position: absolute;
  z-index: 1000;
  top: 20px;
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
}

.d-arrow-left i {
  left: 8px;
}

.d-arrow-right i {
  right: 8px;
}

#d-coll-carousel {
  position: relative;
  display: flex;
  align-items: center;
}

.owl-nav {
  display: flex;
}

.owl-prev,
.owl-next,
.d-nav-left,
.d-nav-right {
  cursor: pointer;
  position: absolute;
  top: 27.5%;
  z-index: 100;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 1.0);
  border: solid 1px #ccc;
}

.dark-scheme .owl-prev,
.dark-scheme .owl-next {
  background: var(--bg-color-even);
  color: #ffffff;
  border-color: rgba(255, 255, 255, .1);
}

.owl-prev:hover,
.owl-next:hover {
  transform: scale(1.1);
  -webkit-box-shadow: 5px 5px 30px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 5px 5px 30px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 5px 5px 30px 0px rgba(0, 0, 0, 0.2);
}

.owl-prev,
.d-nav-left {
  left: -12px;
  border-radius: 60px;
  padding-left: 4px;
}

.owl-next,
.d-nav-right {
  right: -12px;
  border-radius: 60px;
  padding-right: 4px;
}

.d-nav-left i,
.d-nav-right i {
  position: absolute;
  top: 20px;
  color: #111111;
  font-size: 20px;
  font-weight: bold;
}

.owl-next i,
.d-nav-left i {
  top: 12px;
  left: 16px;
}

.d-nav-right i {
  top: 12px;
  right: 16px;
}

.owl-arrow {
  z-index: 100;
  background: #fff;
  margin-top: 48%;
  display: none;
  cursor: poInter;
}

.owl-arrow span {
  display: block;
  background: #555;
  position: absolute;
}

.owl-arrow .prev {
  left: 0;
}

.owl-arrow .next {
  right: 0;
}

.owl-arrow .prev:before,
.owl-arrow .next:before {
  font-family: "FontAwesome";
  content: '\f177';
  color: #222222;
  padding: 20px;
  font-size: 32px;
  display: block;
}

.owl-arrow .next:before {
  content: '\f178';
}

.owl-pagination {
  margin-top: 0px;
  height: 40px;
}

.owl-theme .owl-controls .owl-page span {
  display: block;
  margin-top: 20px;
  margin-left: 10px;
  margin-right: 10px;
  width: 6px;
  height: 6px;
  background-color: #555;
  opacity: 1;
}

.owl-theme .owl-controls .owl-page.active span {
  background: none;
}

.owl-theme .owl-controls .owl-page.active span:before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  -moz-border-radius: 15px;
  -webkit-border-radius: 15px;
  border-radius: 15px;
  position: relative;
  border: solid 2px #555;
  top: -2px;
  left: -2px;
}

/* new owl */

.owl-theme .owl-nav.disabled+.owl-dots {
  margin-top: 0px;
}

.owl-dots button {
  border: none;
  outline: none;
  padding: 0;
  width: 8px;
  height: 8px;
  margin: 0 5px;
  border-radius: 30px;
  background: rgba(0, 0, 0, .15);
}

.owl-theme .owl-dots .owl-dot span {
  display: none;
}

/* new owl close */

.text-light .owl-theme .owl-controls .owl-page span {
  background-color: #fff;
}

.text-light .owl-theme .owl-controls .owl-page.active span {
  background: none;
}

.text-light .owl-theme .owl-controls .owl-page.active span:before {
  border: solid 2px #fff;
}

.text-slider {
  color: #fff;
  display: inline-block;
}

.text-slider.med-text {
  font-size: 72px;
  line-height: 84px;
  font-weight: bold;
}

.text-slider.big-text {
  font-size: 120px;
  line-height: 120px;
  font-weight: bold;
}

.border-deco .text-item {
  display: inline-block;
}

.text-slider.dark {
  color: #222;
}

.text-item i {
  font-size: 60px;
  margin-top: 5px;
}

.text-slider.big-text i {
  font-size: 90px;
  margin-top: 15px;
}

.text-slider h1 {
  font-size: 60px;
}

.text-rotate-wrap {
  display: inline-block;
}

.text-rotate-wrap .text-item {
  display: none;
}

.red {
  color: #ff0000;
}

.teal {
  color: #009999;
}

.purple {
  color: #663366;
}

.green {
  color: #009900;
}

.bg-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.center-y {
  position: relative;
}

.overlay-bg {
  padding: 120px 0;
  width: 100%;
  height: 100%;
  z-index: 100;
}

.overlay-bg.p60 {
  padding-top: 60px;
  padding-bottom: 60px;
}

.overlay-gradient {
  width: 100%;
  height: 100%;
  z-index: 100;
  top: 0;
}

.overlay-solid {
  width: 100%;
  height: 100%;
  z-index: 100;
  top: 0;
}

.overlay-bg.t0 {
  background: rgba(var(--primary-color-rgb), 0);
}

.overlay-bg.t20 {
  background: rgba(var(--primary-color-rgb), .2);
}

.overlay-bg.t30 {
  background: rgba(var(--primary-color-rgb), .3);
}

.overlay-bg.t40 {
  background: rgba(var(--primary-color-rgb), .4);
}

.overlay-bg.t50 {
  background: rgba(var(--primary-color-rgb), .5);
}

.overlay-bg.t60 {
  background: rgba(var(--primary-color-rgb), .6);
}

.overlay-bg.t70 {
  background: rgba(var(--primary-color-rgb), .7);
}

.overlay-bg.t80 {
  background: rgba(var(--primary-color-rgb), .8);
}

.overlay-bg.t90 {
  background: rgba(var(--primary-color-rgb), .9);
}


.overlay-bg.light-5 {
  background: rgba(255, 255, 255, .5);
}

.overlay-bg.light-6 {
  background: rgba(255, 255, 255, .6);
}

.overlay-bg.light-7 {
  background: rgba(255, 255, 255, .7);
}

.overlay-bg.light-8 {
  background: rgba(255, 255, 255, .8);
}

.overlay-bg.c1 {
  background: rgba(var(--primary-color-rgb), .8);
}

.overlay-bg.dark {
  background: rgba(20, 20, 20, .8);
}

.overlay-bg.gradient-blue-aqua {
  background: -moz-linear-gradient(45deg, rgba(2, 68, 129, 0.9) 0%, rgba(52, 176, 188, 1) 100%);
  background: -webkit-linear-gradient(45deg, rgba(2, 68, 129, 0.9) 0%, rgba(52, 176, 188, 1) 100%);
  background: linear-gradient(45deg, rgba(2, 68, 129, 0.9) 0%, rgba(52, 176, 188, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#cc024481', endColorstr='#cc34b0bc', GradientType=1);
}


.de-overlay-gradient-dark {
  position: absolute;
  width: 100%;
  height: 100%;
  top:0;
  left: 0;
  background: -moz-linear-gradient(rgba(0 ,0 ,0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  background: -webkit-linear-gradient(rgba(0 ,0 ,0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  background: linear-gradient(rgba(0 ,0 ,0, 0) 0%, rgba(0, 0, 0, 1) 100%);
}


.de-overlay-gradient-color {
  position: absolute;
  width: 100%;
  height: 100%;
  top:0;
  left: 0;
  background: -moz-linear-gradient(rgba(var(--primary-color-rgb), 0) 0%, rgba(var(--primary-color-rgb), 1) 100%);
  background: -webkit-linear-gradient(rgba(var(--primary-color-rgb), 0) 0%, rgba(var(--primary-color-rgb), 1) 100%);
  background: linear-gradient(rgba(var(--primary-color-rgb), 0) 0%, rgba(var(--primary-color-rgb), 1) 100%);
}


.bg-grey,
section.bg-grey {
  background: var(--bg-grey);
}

.wow {
  visibility: hidden;
}

#back-to-top {
  background: var(--primary-color);
  position: fixed;
  bottom: -40px;
  right: 20px;
  z-index: 1020;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 30px;
  cursor: poInter;
  padding-top: 3px;
  border: 0;
  text-decoration: none;
  transition: opacity 0.2s ease-out;
  outline: none;
  opacity: 0;
  border-radius:30px;
  -moz-border-radius:30px;
  -webkit-border-radius:30px;
}

#back-to-top:hover {
  transform: scale(1.1);
}

#back-to-top.show {
  bottom: 20px;
  opacity: 1;
}

#back-to-top.hide {
  bottom: -40px;
}

#back-to-top:before {
  font-family: "FontAwesome";
  font-size: 18px;
  content: "\f106";
  color: #ffffff;
  position: relative;
}

/* mouse scroll icon begin */

.mouse {
  position: absolute;
  width: 22px;
  height: 42px;
  bottom: 40px;
  left: 50%;
  margin-left: -12px;
  border-radius: 15px;
  border: 2px solid #fff;
  -webkit-animation: intro 1s;
  animation: intro 1s;
}

.scroll {
  display: block;
  width: 3px;
  height: 3px;
  margin: 6px auto;
  border-radius: 4px;
  background: #fff;
  -webkit-animation: finger 1s infinite;
  animation: finger 1s infinite;
}

@-webkit-keyframes intro {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes intro {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@-webkit-keyframes finger {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }
}

@keyframes finger {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }
}

/* mouse scroll icon close */

@-moz-keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    -moz-transform: translateY(0);
    transform: translateY(0);
  }

  40% {
    -moz-transform: translateY(-30px);
    transform: translateY(-30px);
  }

  60% {
    -moz-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

@-webkit-keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  40% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }

  60% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  40% {
    -moz-transform: translateY(-30px);
    -ms-transform: translateY(-30px);
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
  }

  60% {
    -moz-transform: translateY(-15px);
    -ms-transform: translateY(-15px);
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
  }
}

.arrow-anim {
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -20px;
  width: 40px;
  height: 40px;
  background-size: contain;
}

.arrow-anim:before {
  content: "\f107";
  font-family: "FontAwesome";
  font-size: 32px;
}

.bounce {
  -moz-animation: bounce 2s infinite;
  -webkit-animation: bounce 2s infinite;
  animation: bounce 2s infinite;
}

a.read_more {
  display: inline-block;
  font-weight: bold;
  color: #222222;
  text-decoration: none;
}

a.read_more:hover {
  color: #707070;
}

a.read_more i {
  margin-left: 8px;
}

.text-light a.read_more {
  color: #fff;
}

.text-black {
  color: #222;
}


#de-arrow-nav-set {
  text-align: center;
}

#de-arrow-nav-set i {
  color: #222;
  font-size: 32px;
}

#de-arrow-nav-set i.icon_menu {
  display: inline-block;
}

/* owl */
.owl-custom-nav.menu-float{
  position: relative;
}
.owl-custom-nav.menu-float .btn-prev,
.owl-custom-nav.menu-float .btn-next{
  position: absolute;
  z-index: 2000;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  cursor: pointer;
  padding:0;
}
.owl-custom-nav.menu-float .btn-prev{
  left: 0px;
}
.owl-custom-nav.menu-float .btn-next{
  right: 0px;
}
.owl-custom-nav.menu-float .btn-prev:before,
.owl-custom-nav.menu-float .btn-next:before{
  font-size: 24px;
}
.owl-custom-nav .btn-prev:before,
.owl-custom-nav .btn-next:before{
display: block;
font-family:"FontAwesome";
content:"\f105";
color:#ffffff;
padding:8px 0;
font-size:20px;
width: 40px;
height: 50px;
background: var(--primary-color);
text-align: center;
}
.owl-custom-nav .btn-prev:before{
content:"\f104";
border-radius:0 30px 30px 0;
-moz-border-radius:0 30px 30px 0;
-webkit-border-radius:0 30px 30px 0;
}
.owl-custom-nav .btn-next:before{
border-radius:30px 0 0 30px;
-moz-border-radius:30px 0 0 30px;
-webkit-border-radius:30px 0 0 30px;
}

.owl-custom-nav.dark .btn-prev:before,
.owl-custom-nav.dark .btn-next:before{
  color:#000000;
}

.owl-custom-nav a{
  text-decoration:none;
}

.owl-prev,.owl-next{
  display: none;
}

#custom-owl-slider {
  display: block;
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: -1;
  background: #fff;
}

#custom-owl-slider .items {
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 20;
  visibility: inherit;
  opacity: 1;
}

#custom-owl-slider .item img {
  display: block;
  width: 100%;
  height: 100%;
}

.owl-slider-nav {
  position: absolute;
  width: 100%;
}

.owl-slider-nav,
.owl-slider-nav div {
  position: absolute;
  z-index: 10;
  cursor: poInter;
}

.owl-slider-nav .next {
  right: -50px;
  width: 0;
  height: 0;
  border-top: 50px solid transparent;
  border-bottom: 50px solid transparent;
  border-right: 50px solid transparent;
}

.owl-slider-nav .prev {
  left: -50px;
  width: 0;
  height: 0;
  border-top: 50px solid transparent;
  border-bottom: 50px solid transparent;
  border-left: 50px solid transparent;
}

.owl-slider-nav .next:before {
  font-family: "FontAwesome";
  font-size: 24px;
  content: "\f0da";
  color: #fff;
  position: absolute;
  right: -40px;
  top: -13px;
}

.owl-slider-nav .prev:before {
  font-family: "FontAwesome";
  font-size: 24px;
  content: "\f0d9";
  color: #fff;
  position: absolute;
  left: -40px;
  top: -13px;
}

.owl-dots {
  text-align: center;
}

.owl-dot {
  background: #ccc;
  border: none;
  margin: 5px;
  width: 6px;
  height: 6px;
  padding: 0;
  border-radius: 60px;
  -moz-border-radius: 60px;
  -webkit-border-radius: 60px;
}

.dark-scheme .owl-dot,
.text-light .owl-dot {
  background: rgba(255, 255, 255, .2);
}

.owl-item.active div blockquote {
  position: relative;
  overflow: hidden;
}

.owl-item.active div blockquote,
.owl-item.active div blockquote .de_testi_by {
  opacity: 1;
}

.owl-stage {
  overflow: none;
}

.typed-strings p {
  position: absolute;
}

.typed-cursor {
  opacity: 1;
  -webkit-animation: blink 0.5s infinite;
  -moz-animation: blink 0.5s infinite;
  animation: blink 0.5s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-moz-keyframes blink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.style-2 .date-box {
  text-align: center;
  position: absolute;
  right: 0;
  padding: 15px 5px 5px 5px;
  color: #fff;
}

.style-2 .date-box .d {
  font-weight: 600;
  font-size: 38px;
  margin-bottom: 5px;
}

.style-2 .date-box .m {
  font-family: var(--body-font);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 8px;
  margin-left: 8px;
}

.sitemap {
  margin: 0;
  padding: 0;
}

.sitemap.s1 li {
  list-style: none;
}

.sitemap.s1 li:before {
  font-family: FontAwesome;
  content: "\f016";
  margin-right: 10px;
}

.list {
  margin: 0;
  padding: 0;
}

.list li {
  list-style: none;
}

.rtl .list.s1 li:after,
.list.s1 li:before {
  font-family: FontAwesome;
  content: "\f00c";
  margin-right: 10px;
}

.rtl .list.s1 li:after {
  margin-right: 0;
  margin-left: 10px;
}

.rtl .list.s1 li:before {
  display: none;
}

.list.s2 li:before {
  font-family: FontAwesome;
  content: "\f055";
  margin-right: 10px;
}

.list.s3 {
  display: inline;
  margin: 0;
  padding: 0;
}

.list.s3 li {
  display: inline;
  margin-right: 20px;
}

.list.s3 a {
  font-weight: bold;
}

#owl-logo img:hover {
  opacity: 1;
}

.switch-set {
  font-weight: bold;
  direction: ltr !important;
}

.dark-scheme .switch-set{
  color:#ffffff;
}

.dark-scheme .switch {
  background: rgba(255, 255, 255, .1);
}

.switch-set div {
  display: inline-block;
  padding: 0 5px;
  direction: ltr !important;
}

.switch {
  margin-bottom: -5px;
  -webkit-appearance: none;
  height: 22px;
  width: 42px;
  background-color: #F4F4F4;
  border-radius: 60px;
  position: relative;
  cursor: poInter;
  border: none;
}

.switch::after {
  content: "";
  width: 18px;
  height: 18px;
  background-color: #ffffff;
  position: absolute;
  border-radius: 100%;
  transition: .5s;
  margin: 2px 0 0 2px;
}

.de-switch {
  display: block;
}

.de-switch h3 {
  font-weight: 400;
  padding-bottom: 6px;
}

.de-switch input[type=checkbox] {
  display: none;
}

.de-switch input[type=checkbox]:checked+label {
  background-color: #2f7df9;
}

.de-switch input[type=checkbox]:checked+label:after {
  left: 23px;
}

.de-switch label {
  transition: all 200ms ease-in-out;
  display: inline-block;
  position: relative;
  height: 20px;
  width: 40px;
  border-radius: 40px;
  cursor: pointer;
  background-color: rgba(0, 0, 0, .3);
  color: transparent;
}

.dark-scheme .de-switch label {
  background-color: rgba(255, 255, 255, .1);
}

.de-switch label:after {
  transition: all 200ms ease-in-out;
  content: " ";
  position: absolute;
  height: 14px;
  width: 14px;
  border-radius: 50%;
  background-color: white;
  top: 3px;
  left: 3px;
  right: auto;
}

.de-switch.colored input[type=checkbox]:checked+label {
  background-color: #55c946;
}

.de-switch.colored label {
  background-color: #ff4949;
}

.switch-with-title h5 {
  float: left;
}

.switch-with-title .de-switch {
  float: right;
}

.switch:checked::after {
  transform: translateX(20px);
}

.switch:focus {
  outline-color: transparent;
}

.menu_side_area {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.rtl .menu_side_area {
  margin-left: 0;
}

.de_countdown {
  text-align: right;
  z-index: 100;
  padding: 0 0 10px 0;
}

.dark-scheme .de_countdown {
  color: #fff;
  background: none;
}

.countdown-section {
  font-size: 28px;
  margin-left: 8px;
  font-weight: bold;
}

.tabs_wrapper {
  margin-top: 40px;
}

.hide-content {
  display: none;
}

.display-none {
  display: none;
}

.ul-check {
  padding: 0;
  list-style: none;
}

.ul-check li {
  position: relative;
  display: block;
  margin-top: 0;
  padding: 0px;
  padding-left: 30px;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  line-height: 1.6em;
}

.ul-check li h4 {
  font-size: 18px;
  font-weight: 500;
}

.ul-check.s2 li h4 {
  font-weight: 600;
}

.ul-check.s2 li {
  margin-bottom: 30px;
}

.ul-check.s3 li {
  margin-bottom: 5px
}

.ul-check .d-label {
  background: #fdda76;
  font-weight: bold;
  padding: 0 5px;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
}

.ul-check-inline{
  margin: 0;
  padding: 0;
}

.ul-check-inline li {
  display: inline-block;
  padding: 0 20px;
  padding-left: 0;
  color: var(--heading-font-color);
}

.ul-check-inline li:before {
  font-family: "FontAwesome";
  content: "\f00c";
  margin-right: 10px;
  color: var(--primary-color);
  border: solid 1px var(--primary-color);
  border-radius: 30px;
  -moz-border-radius: 30px;
  -webkit-border-radius: 30px;
  padding: 5px;
}

.ul-check li:before {
  position: absolute;
  left: 0;
  font-family: "FontAwesome";
  content: "\f00c";
  margin-right: 12px;
  width: 20px;
  line-height: 1;
  padding: 4px 0;
  font-size: 15px;
  display: inline-block;
  text-align: center;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
}

.rtl .ul-check li:before {
  margin-right: 0;
  margin-left: 12px;
}

.ul-style-3 {
  margin-top: 15px;
  padding: 0;
  list-style: none;
}

.ul-style-3 li {
  display: inline-block;
  margin: 0;
  padding: 0px;
}

.ul-style-3 li:after {
  content: "/";
  margin: 0 5px 0 10px;
}

.ul-style-3 li:last-child:after {
  content: "";
}

a:hover .bg-hover-2{
  background: var(--primary-color) !important;
}

a:hover .text-hover-white{
  color:#ffffff;
}


/* shadow */

.shadow-soft,
.de-map-wrapper iframe,
.expand-custom,
#form_sb input,
.container-timeline ul li::before{
  -webkit-box-shadow: 0 10px 30px rgb(0 19 87 / 6%);
  -moz-box-shadow: 0 10px 30px rgb(0 19 87 / 6%);
  box-shadow: 0 10px 30px rgb(0 19 87 / 6%);
}

.shadow-1 {
  -webkit-box-shadow: 0 10px 30px rgb(29, 29, 31, 25%);
  -moz-box-shadow: 0 10px 30px rgb(29, 29, 31, 25%);
  box-shadow: 0 10px 30px rgb(29, 29, 31, 25%);
}

.no-shadow {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.rounded-5 {
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  position: relative;
  overflow: hidden;
}


.rounded-10 {
  overflow: hidden;
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
}

.rounded-20 {
  border-radius: 20px;
  -moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  position: relative;
}

.rounded-30{  
  overflow: hidden;
  border-radius:20px;
  -moz-border-radius:20px;
  -webkit-border-radius:20px;
}

.circle{
  border-radius:100%;
  -moz-border-radius:100%;
  -webkit-border-radius:100%;
}

.rtl .carousel-control-next {
  right: 0;
}

.rtl .carousel-control-prev {
  left: 0;
  right: auto;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
  opacity: 1;
  width: 48px;
  height: 48px;
  padding-top: 10px;
  border-radius:10px;
  -moz-border-radius:10px;
  -webkit-border-radius:10px;
}

.carousel-control-next-icon:after,
.carousel-control-prev-icon:after {
  font-family: FontAwesome;
  content: "\f104";
  font-size: 32px;
  color: var(--primary-color);
}

.carousel-control-next-icon:after {
  content: "\f105";
}

.carousel-control-next-icon:hover,
.carousel-control-prev-icon:hover {
  background: #ffffff;
}

.carousel-indicators [data-mdb-target] {
  background-color: var(--primary-color);
  border-radius: 100%;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
}

.de-map-wrapper iframe {
  width: 100%;
  height: 450px;
  border: solid 1px #cccccc;
}


/* image effect */


img.anim-up-down {
  -webkit-animation: move-up-down 1s infinite alternate;
  animation: move-up-down 1s infinite alternate;
}

img.anim-up-down {
  -webkit-animation: move-up-down 1s infinite alternate;
  animation: move-up-down 1s infinite alternate;
}

@-webkit-keyframes move-up-down {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-10px);
  }
}

@keyframes move-up-down {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-10px);
  }
}

.slider-text-wrap {
  background: rgba(255, 255, 255, .75);
  padding: 30px;
  padding-top: 20px;
  overflow: hidden;
  border-radius: 8px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
}

.bubble{
  font-weight: bold;
  position: absolute;
  color:#ffffff;
  text-align: center;
  width: 100px;
  height: 100px;
  border-radius:100%;
  padding-top: 35px;
  -moz-border-radius:100%;
  -webkit-border-radius:100%;
  background: var(--primary-color);
  z-index:1;
}

.bubble-3 .bubble:nth-child(1){
  position: absolute;
  top:25%;
  left: 10%;
}

.bubble-3 .bubble:nth-child(2){
  position: absolute;
  top:35%;
  right: 10%;
}

.bubble-3 .bubble:nth-child(3){
  position: absolute;
  bottom:15%;
  left: 30%;
}

.d-grid-images{
  border-top: solid 1px #dddddd;
  border-left: solid 1px #dddddd;
}

.d-grid-images .d-item{
  text-align: center;
  display: flex;
  width: 25%;
  height: 100px;
  float: left;
  border-right: solid 1px #dddddd;
  border-bottom: solid 1px #dddddd;
  justify-content: center;
  align-items: center;
}

/* price list */

.def-list-dots{
  padding-left: 30px;
  font-size: 18px;
}

.def-list-dots dl {
  display: table;
  width: 100%;
  margin: 3px 0;
  margin-bottom: 10px;
}
.def-list-dots dt {
  display: table-cell;
  width: 100%;
  max-width: 0;
  overflow: hidden;
  vertical-align: top;
  font-weight: 400;
}
.def-list-dots dt span:after {
  content: " ";
}
.def-list-dots dt:after {
  content: "..................................................................................................................................................................................................................";
  display: inline-block;
  width: 0;
  color: rgba(255,255,255, 0.5);
}
.def-list-dots .no-def-item dt:after {
  content: "";
}
.def-list-dots dd {
  display: table-cell;
  margin: 0;
  padding-left: 4px;
  vertical-align: bottom;
  white-space: nowrap;
  font-weight: 600;
  color:var(--primary-color);
}

.images-carousel-group img{
  height: 350px;
  width: auto;
}

.d-list-menu {
  margin: 0;
  padding: 0;
}
#search {
  float: left;
  width: 70%;
  margin-bottom: 0;
}

#btn-search {
  border: none;
  padding: 7px;
  background: #222;
  float: left;
  width: 30%;
  height: 42px;
}

#btn-search:before {
  font-family: FontAwesome;
  content: "\f002";
  font-size: 24px;
  color: #111;
}

#btn-search:hover {
  background: #fff;
}

/* dropdown */
/* ------------------------------ */
.dropdown {
  position: relative;
  z-index: 500;
  font-weight: 600;
  display: inline-block;
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  text-align: left;
}

.dropdown a,
.dropdown a:hover {
  text-decoration: none;
  outline: none;
  display: inline-block;
}

.dropdown a,
.header-light .dropdown a:hover {
  color: #333;
}

.dark-scheme .dropdown a,
.dark-scheme .header-light .dropdown a:hover {
  color: #ffffff;
}

.dropdown>a {
  display: inline-block;
  padding: 7px 12px 7px 12px;
  min-width: 140px;
  border: solid 1px rgba(0, 0, 0, .2);
  border-radius: 0px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  font-weight: bold;
}

.dark-scheme .dropdown>a {
  border: solid 1px rgba(255, 255, 255, .2);
}

.dropdown>a:after {
  font-family: "FontAwesome";
  font-size: 16px;
  content: "\f107";
  position: relative;
  float: right;
  margin-left: 10px;
}

.rtl .dropdown>a:after {
  margin-left: 0;
  float: left;
}

.dropdown ul,
.dropdown li {
  list-style: none;
  display: block;
  padding: 0;
  margin: 0;
}

.dropdown ul {
  background: #ffffff;
  padding: 10px;
  position: absolute;
  min-width: 140px;
  display: none;
  cursor: pointer;
  border: solid 1px rgba(0, 0, 0, .1);
}

.dropdown li span {
  background: #ffffff;
  display: block;
  padding: 5px 10px 5px 10px;
  border-top: none;
  width: 100%;
  font-weight: 400;
  font-size: 14px;
}

.dark-scheme .dropdown li span {
  background: var(--bg-dark-1);
  border: solid 1px rgba(255, 255, 255, .1);
}

.dark-scheme .dropdown li span:hover {
  background: var(--bg-color-even);
  color: #fff;
}

.dropdown li span:hover {
  color: #ffffff;
  background: #eeeeee;
}

.dropdown li.active {
  display: none;
}

.dropdown ul.d-col-3 {
  width: 600px !important;
}

.dropdown ul.d-col-3 li {
  width: 33.3%;
  float: left;
}

.dropdown ul.d-col-3 li span {}

.text-light .dropdown {
  background: none;
  border: solid 1px rgba(255, 255, 255, .1);
}

.text-light .dropdown a {
  color: #ffffff;
}

.items_filter {
  font-size: 14px;
  margin-bottom: 30px;
}

.items_filter .dropdown {
  margin-right: 10px;
}

.dropdown.fullwidth,
.dropdown.fullwidth a,
.dropdown.fullwidth ul,
.dropdown.fullwidth li {
  width: 100%;
}

.dropdown.style-2,
.dropdown.style-2 a,
.dropdown.style-2 ul,
.dropdown.style-2 li {
  width: 100%;
}

.rtl .dropdown.style-2 {
  text-align: right;
}

.dropdown.style-2 h4 {
  font-size: 15px;
  font-weight: 500;
}

.dropdown.style-2 a {
  font-size: 20px;
  color: #ffffff;
  padding: 5px 0;
  border: none;
  border-top: solid 1px rgba(255, 255, 255, .3);
}

a.btn-search-big {
  text-align: center;
  display: inline-block;
  width: 100%;
  padding: 20px;
  font-weight: bold;
  color: #ffffff;
  font-size: 20px;
  border: solid 1px #ffffff;
}

a.btn-search-big:hover {
  background: #ffffff;
  color: #35404e;
}

.separator {
  line-height: 0.5;
  text-align: center;
  margin: 30px 0 30px 0;
}

.separator span {
  display: inline-block;
  position: relative;
}

.separator span:before,
.separator span:after {
  content: "";
  position: absolute;
  border-bottom: 1px solid rgba(255, 255, 255, .1);
  top: 5px;
  width: 50px;
}

.separator span:before {
  right: 100%;
  margin-right: 15px;
}

.separator span:after {
  left: 100%;
  margin-left: 15px;
}

.separator span i {
  font-size: 12px;
  padding: 0;
  margin: 0;
  float: none;
}

.de_light .separator span:before,
.de_light .separator span:after {
  border-bottom: 1px solid rgba(0, 0, 0, .2);
}

.text-light .separator span:before,
.text-light .separator span:after {
  border-bottom: 1px solid rgba(255, 255, 255, .1);
}

.de-separator:after {
background: none repeat scroll 0% 0% var(--primary-color);
content: "";
height: 10px;
right: 0%;
margin: -5px auto auto -5px;
position: absolute;
transform: rotate(-45deg);
width: 10px;
}
.de-separator:before {
background: none repeat scroll 0% 0% var(--primary-color);
content: "";
height: 10px;
left: 0%;
margin: -5px auto auto -5px;
position: absolute;
transform: rotate(-45deg);
width: 10px;
}
.de-separator {
position: relative;
border-top: 1px solid var(--primary-color);
width: 250px;
clear: both;
margin:0 auto;
margin-bottom: 40px;
}  

/* --------------------------------------------------
* plugin
* --------------------------------------------------*/

/* ================================================== */


/* isotope */


/* ================================================== */

.isotope,
.isotope .isotope-item {
  -webkit-transition-duration: 1s;
  -moz-transition-duration: 1s;
  -ms-transition-duration: 1s;
  -o-transition-duration: 1s;
  transition-duration: 1s;
}

.isotope {
  -webkit-transition-property: height, width;
  -moz-transition-property: height, width;
  -ms-transition-property: height, width;
  -o-transition-property: height, width;
  transition-property: height, width;
}

.isotope .isotope-item {
  -webkit-transition-property: -webkit-transform, opacity;
  -moz-transition-property: -moz-transform, opacity;
  -ms-transition-property: -ms-transform, opacity;
  -o-transition-property: -o-transform, opacity;
  transition-property: transform, opacity;
}

/**** disabling Isotope CSS3 transitions ****/

.isotope.no-transition,
.isotope.no-transition .isotope-item,
.isotope .isotope-item.no-transition {
  -webkit-transition-duration: 0s;
  -moz-transition-duration: 0s;
  -ms-transition-duration: 0s;
  -o-transition-duration: 0s;
  transition-duration: 0s;
}

#filters {
  font-family: var(--body-font);
  padding: 0;
  margin: 0;
  margin-bottom: 20px;
  font-weight: 400;
}

#filters li {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 25px;
}

#filters a {
  outline: none;
  text-decoration: none;
  padding: 8px 20px;
  border-radius: 6px;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  font-weight: bold;
}

.text-light #filters a {
  color: rgba(255, 255, 255, .5);
}

#filters a:hover {  
  background: rgba(255, 255, 255, .2);
}

#filters a.selected {
  color: #fff;
}

.text-light #filters a:hover {
  color: #fff;
}

#filters a.selected {
  color: #ffffff;
  background: var(--primary-color);
}

.isotope-item {
  z-index: 2;
}

.isotope-hidden.isotope-item {
  poInter-events: none;
  z-index: 1;
}

.project-carousel ul.slides,
.project-carousel-3-col ul.slides {
  margin-top: 10px;
  margin-bottom: 20px;
}

.logo-carousel .flex-direction-nav,
.project-carousel .flex-control-nav,
.project-carousel .flex-direction-nav,
.project-carousel-3-col .flex-control-nav,
.project-carousel-3-col .flex-direction-nav {
  display: none;
}

.logo-carousel.owl-carousel .owl-item img {
  width: auto;
}

.logo-carousel.no-control .flex-control-nav {
  display: none;
}

.logo-carousel.s2 img {
  background: rgba(255, 255, 255, .25);
  border-radius: 10px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  padding: 10px;
}

.owl-theme .owl-dots .owl-dot.active {
  background: var(--primary-color);
}
.owl-carousel.owl-center .owl-item.active.center {
    opacity: 1;
     position: relative;
     z-index: 101;
     -webkit-transform: scale(1.2);
     transform: scale(1.2);
     -webkit-box-shadow: 8px 8px 10px 0px rgba(0, 0, 0, 0.calc());
    -moz-box-shadow: 8px 8px 10px 0px rgba(0, 0, 0, 0.calc());
    box-shadow: 8px 8px 10px 0px rgba(0, 0, 0, 0.calc());
}

.typed-strings p {
  position: absolute;
}

.social-icons-sm i {
  color: #fff;
  margin: 0 5px 0 5px;
  border: solid 1px #555;
  width: 32px;
  height: 32px;
  padding: 8px;
  text-align: center;
  border-radius: 30px;
}

.social-icons-sm i:hover {
  color: #ffffff;
}

.arrowup {
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 30px solid #fff;
  margin: 0 auto;
  left: 0;
  right: 0;
  position: absolute;
  margin-top: -30px;
}

.arrowdown {
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-top: 30px solid #ffffff;
  margin: 0 auto;
  left: 0;
  right: 0;
  position: absolute;
}

.arrowright {
  width: 0;
  height: 0;
  border-top: 60px solid transparent;
  border-bottom: 60px solid transparent;
  border-left: 60px solid green;
}

.arrowleft {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid blue;
}

a,
img {
  outline: 0;
}

/* overwrite bootstrap */

.nav-pills .nav-link {
  font-weight: 500;
}

.de-preloader {
  width: 100%;
  height: 100%;
  top: 0;
  position: fixed;
  background: #ffffff;
  z-index: 5000;
}

.dark-scheme .de-preloader {
  background: var(--bg-color-even);
}

.dot-loader {
  width: 80px;
  height: 16.6666666667px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.dot-loader-dot {
  will-change: transform;
  height: 16.6666666667px;
  width: 16.6666666667px;
  border-radius: 50%;
  background-color: #ffffff;
  position: absolute;
  -webkit-animation: grow 0.5s ease-in-out infinite alternate;
  animation: grow 0.5s ease-in-out infinite alternate;
}

.dot-loader-dot.dot1 {
  left: 0;
  transform-origin: 100% 50%;
}

.dot-loader-dot.dot2 {
  left: 50%;
  transform: translateX(-50%) scale(1);
  -webkit-animation-delay: 0.15s;
  animation-delay: 0.15s;
}

.dot-loader-dot.dot3 {
  right: 0;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

@-webkit-keyframes grow {
  to {
    transform: translateX(-50%) scale(0);
  }
}

@keyframes grow {
  to {
    transform: translateX(-50%) scale(0);
  }
}


/* -------------------------Line Preloader--------------------- */

.line-preloader {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 80px;
  height: 16.6666666667px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);

}

.line-preloader .p-line {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0);
}

.line-preloader .p-line:nth-child(1) {
  position: absolute;
  width: 25px;
  height: 25px;
  border-right: 2px solid gold;
  border-bottom: 2px solid gold;
  animation: anim4 600ms linear infinite;
}

.line-preloader .p-line:nth-child(2) {
  border-left: 2px solid gold;
  border-top: 2px solid gold;
  animation: anim4 1200ms linear infinite;
}

.line-preloader .p-line:nth-child(3) {
  position: absolute;
  width: 75px;
  height: 75px;
  border-right: 2px solid gold;
  border-top: 2px solid gold;
  animation: anim4 900ms linear infinite;
}

@keyframes anim4 {
  to {
    transform: rotate(360deg);
  }
}


.de-step-icon {
      width: 80px;
      height: 80px;
      background-color: var(--bg-dark-1);
      color: white;
      border-radius: var(--rounded-1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      margin: 0 auto 20px;
    }

    .de-step-icon i{
      font-size: 32px;
    }

    .de-step {
      text-align: center;
      position: relative;
      padding: 20px;
    }

    .de-step-arrow::after {
      content: "→";
      font-size: 24px;
      position: absolute;
      right: -25px;
      top: 50%;
      transform: translateY(-50%);
      color: #000;
    }

    .dark-scheme .de-step-arrow::after, .text-light .de-step-arrow::after {
      color: #ffffff;
    }

    .rtl .de-step-arrow::after{
      display: none;
    }

    .rtl .de-step-arrow::before {
      content: "←";
      font-size: 24px;
      position: absolute;
      right: auto;
      left:  -25px;
      top: 50%;
      transform: translateY(50%);
      color: #000;
    }

    .de-step:last-child .de-step-arrow::after {
      content: none;
    }

    @media (max-width: 767.98px) {
      .de-step-arrow::after {
        display: none;
      }
    }

.d-tag{
  border: solid 1px rgba(0, 0, 0, .5);
  display: inline-block;
  padding: 0 10px;
  border-radius:var(--rounded-1);
  -moz-border-radius:var(--rounded-1);
  -webkit-border-radius:var(--rounded-1);
  margin-right: 5px;
}

/* demo */

#selector {
    width: 500px;
    position: fixed;
    right: 0;
    top: 25%;
    padding: 5px;
    z-index: 1000;
}

#selector .opt {
    margin-top: 10px;
    margin-right: 5px;
    width: 20px;
    height: 20px;
    display: block;
    float: left;
    background: #ffffff;
    border-radius: 30px;
    -moz-border-radius: 30px;
    -webkit-border-radius: 30px;
    cursor: pointer;
    border: solid 1px rgba(255, 255, 255, .5);
}

#selector .opt:hover {
    border-color: #ffffff;
}

#selector .opt.tc1 {
    background: #E9021E;
}

#selector .opt.tc2 {
    background: #1277FF;
}

#selector .opt.tc3 {
    background: #FF6827;
}

#selector .opt.tc4 {
    background: #4CBB17;
}

#selector .opt.tc5 {
    background: #0073CF;
}

#selector .opt.tc6 {
    background: #33C9BD;
}

#selector .opt.tc7 {
    background: #AE9749;
}

#selector .opt:after {
    position: relative;
    display: inline-block;
    content: '\f00c';
    font-family: "FontAwesome";
    top: -5px;
    opacity: 0;
    transform: scale(2);
}

#selector .opt.active:after {
    opacity: 1;
    transform: scale(1);
}

#selector #dark-mode,
#selector #related-items {
    font-weight: bold;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin-top: 50px;
    right: 15px;
    width: 40px;
    height: 39px;
    position: absolute;
    background: #222222;
    padding: 8px;
    padding-top: 0px;
    cursor: pointer;
    overflow: hidden;
    border-radius: 60px;
    -moz-border-radius: 60px;
    -webkit-border-radius: 60px;
    color: #FDD32A;
    font-size: 16px;
}

#selector #related-items {
    margin-top: 100px;
    background: #82b440;
    color: #ffffff;
}

#selector #dark-mode:hover,
#selector #related-items:hover {
    width: 190px;
}

#selector #dark-mode:before,
#selector #related-items:before {
    font-weight: normal;
    display: inline-block;
    content: '\f186';
    font-family: "FontAwesome";
    font-size: 20px;
    padding: 3px;
    padding-top: 6px;
    margin-right: 10px;
}

#selector #related-items:before {
    content: '\f05a';
}

#selector #dark-mode.active {
    background: #FDD32A;
    color: #222222;
}

.disable-dark #selector .dark-mode {
    display: none;
}

#selector .sc-opt {
    cursor: pointer;
    color: #fff;
    font-size: 14px;
    height: 38px;
    overflow: hidden;
    position: absolute;
    text-align: center;
    max-width: 45px;
    padding-right: 20px;
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
    right: 10px;
    background: var(--bg-dark-1);
}

#selector .sc-opt:hover {
    max-width: 100%;
}

#selector .sc-opt .sc-icon {
    width: 45px;
    height: 38px;
    color: #fff;
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    line-height: 14px;
    padding: 12px 10px;
    position: relative;
    float: left;
}

#selector .sc-mt {
    margin-top: 45px;
}

#selector .sc-val {
    display: inline-block;
    margin-top: 5px;
}

.page-item.active .page-link{
  background: var(--primary-color);  
}

.line-through {
    text-decoration: line-through;
}

/* ================================================== */


/* media query */


/* ================================================== */

@media (min-width: 1000px){
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: var(--container-max-width);
    }
}


@media (min-width: 1200px) {

  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1200px;
  }
}


@media (min-width: 1400px) {

  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1304px;
  }
}


/* bootstrap cols 5 */
@media (min-width: 992px) {
  .col-md-1-5 { width: 20%; }
  .col-md-2-5 { width: 40%; }
  .col-md-3-5 { width: 60%; }
  .col-md-4-5 { width: 80%; }
  .col-md-5-5 { width: 100%; }
}

@media (min-width: 1200px) {
  .col-lg-1-5 { width: 20%; }
  .col-lg-2-5 { width: 40%; }
  .col-lg-3-5 { width: 60%; }
  .col-lg-4-5 { width: 80%; }
  .col-lg-5-5 { width: 100%; }
}
/* bootstrap cols 5 end */

@media(min-width:980px) and (max-width: 1199px) {
  #mainmenu li a:after {
    margin-left: 15px;
  }

  .container,
  .container-lg,
  .container-md,
  .container-sm {
    min-width: 95%;
  }
}

/* -------------------------------------------------- 
    design for 768px
    /* ================================================== */

@media (min-width: 768px) and (max-width: 979px) {
  .slider_text {
    padding-top: 0px;
  }
}

@media only screen and (max-width: 992px) {

  .container{
    max-width: 100%;
  }  

  #topbar{
    display: none;
  }

  .de_table .tr .td:nth-child(4),
  .de_table .tr .td:nth-child(5),
  .de_table .tr .td:nth-child(6),
  .de_table .tr .td:nth-child(7) {
    display: none;
  }

  .col-right {
    position: absolute;
    top: -82px;
    right: 60px;
    width: 140px;
  }

  .countdown-s2 .countdown-show4 .countdown-section {
    width: 60px;
    padding:5px;
    margin:5px;
  }

  .countdown-s2 .countdown-period{
    font-size:10px;
  }

  .countdown-s2 .countdown-amount {
    font-size: 20px;
  }


  .mb-sm-20 {
    margin-bottom: 20px;
  }

  .mb-sm-30 {
    margin-bottom: 30px;
  }

  .mt-sm-0{
    margin-top: 0;
  }

  .sm-mt-0 {
    margin-top: 0;
  }

  .p-sm-30 {
    padding: 40px;
  }

  .pb-sm-0 {
    padding-bottom: 0;
  }

  .mb-sm-0 {
    margin-bottom: 0;
  }

  .sm-p-40{
    padding: 40px;
  }

  header .header-col {
    position: absolute;
    top: 0;
  }

  header .header-col.mid {
    position: absolute;
    top: 70px;
    width: 100%;
    padding: 0 0 0 0;
  }

  .text-center-sm {
    text-align: center;
  }

  .sm-pt10 {
    padding-top: 10px;
  }

  #menu-btn {
    display: block;
    float: right;
    z-index: 1000;
    top: 0;
    right: 0;
  }

  .inner-padding {
    padding: 40px;
  }

  .header-col-left,
  .header-col-right {
    padding-top: 16px;
  }

  .header-col-mid #mainmenu {
    position: absolute;
    top: 90px;
    width: 100%;
    padding: 0 0 0 0;
    left: 20px;
  }

  #mainmenu>li::before{
    display: none;
  }

  header {
    display: none;
  }
  
  header.header-bg {
    background: rgba(0, 0, 0, 1);
  }

  header,
  header.fixed {
    display: block;
    position: inherit;
    margin: 0;
    padding: 0;
    margin-top: 0;
    height: auto;
    background: var(--bg-dark-1);
  }

  header.autoshow {
    height: auto;
    top: 0;
  }

  header.autoshow #mainmenu li a {
    background: none;
  }

  header.header-mobile-sticky {
    position: fixed;
  }

  #subheader.s2 {
    padding: 40px 0 30px 0;
  }
  
  #subheader .crumb {
    margin-top: -5px;
    margin-bottom:0;
  }

  #sidebar {
    padding-left: 10px;
  }

  .slider_text h1 {
    font-size: 32px;
  }

  .slider_text .description {
    display: none;
  }

  #logo,
  #logo .inner {
    vertical-align: middle;
    height: auto;
  }

  nav {
    height: 0;
  }

  #mainmenu {
    position: fixed;
    height: 100%;
    z-index: 200;
    width: 200px;
    margin: 0;
    margin-top: 10px;
    padding: 0;
    padding-top: 20px;
    padding-bottom: 100px !important;
    overflow-y: auto;
    top: 100px;
  }

  #mainmenu ul {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }

  #mainmenu a.menu-item {
    text-align: left;
    padding-left: 0;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  #mainmenu li.has-child:after {
    display: none;
  }

  #mainmenu li li a.menu-item,
  #mainmenu li li li a.menu-item {
    padding-left: 0;
  }

  #mainmenu a.menu-item:hover {
    background: #111;
  }

  #mainmenu a.menu-item:hover {
    background: #111;
  }

  #mainmenu li ul {
    display: block;
    position: inherit;
    margin: 0;
    width: 100%;
  }

  #mainmenu li {
    border-bottom: solid 1px var(--bg-dark-3);
    margin: 0;
    width: 100%;
    display: block;
  }

  #mainmenu li a.menu-item:after {
    display: none;
  }

  #mainmenu li:last-child {
    margin-bottom: 30px;
  }

  #mainmenu li ul {
    border-top: solid 1px #eee;
    top: auto;
    width: auto;
    height: auto;
    position: inherit;
    visibility: visible;
    opacity: 1;
  }

  #mainmenu>li.menu-item-has-children>a.menu-item {
    position: relative;
    padding-right: 15px;
  }

  #mainmenu>li.menu-item-has-children>a.menu-item:after {
    content: "\f107";
    font-family: "FontAwesome";
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
  }

  #mainmenu li ul a.menu-item {
    width: 100%;
    background: none;
    border: none;
  }

  #mainmenu li ul li {
    border: none;
    padding-left: 40px;
  }

  #mainmenu li ul li a.menu-item {
    display: block;
  }

  #mainmenu li ul li:last-child {
    border-bottom: none;
    margin: 0;
  }

  #mainmenu li ul li:last-child a.menu-item {
    border-bottom: none;
  }

  #mainmenu li a {
    color: #eceff3;
  }

  #mainmenu li li a.menu-item:hover {
    color: #fff;
  }

  #mainmenu ul:not(.mega) {
    border: none;
  }

  #mainmenu>li ul.mega .sb-menu {
    border: none;
  }

  #back-to-top {
    display: none;
  }

  header.header-mobile {
    overflow: hidden;
    position: relative;
  }

  header.header-mobile #mainmenu {
    width: 100%;
    padding-right: 30px;
    position: absolute;
  }

  header.header-mobile #mainmenu ul {
    background: none;
  }

  header.header-mobile #mainmenu a.menu-item {
    text-align: left;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  header.header-light.header-mobile #mainmenu a.menu-item{
    color:var(--heading-font-color);
  }

  header.header-mobile #mainmenu a.menu-item:hover {
    background: none;
  }

  header.header-mobile #mainmenu li ul {
    display: block;
    position: inherit;
    margin: 0;
    width: 100%;
  }

  header.header-mobile #mainmenu li {
    border-bottom: solid 1px rgba(255, 255, 255, .2);
    margin: 0;
    width: 100%;
    display: block;
  }

  .dark-scheme header.header-mobile #mainmenu li {
    border-bottom-color: rgba(255, 255, 255, .1);
  }

  header.header-mobile #mainmenu li ul.mega ul {
    height: auto;
  }

  header.header-mobile #mainmenu li ul.mega .menu-content {
    background: #fff;
  }

  header.header-light.header-mobile #mainmenu li,
  header.header-light.header-mobile #mainmenu li ul li a.menu-item {
    border-color: #eee;
  }

  header.header-dark.header-mobile {
    background: #222;
  }

  header.header-dark.header-mobile #mainmenu li {
    border-color: #333;
  }

  header.header-dark.header-mobile #mainmenu li a.menu-item {
    color: #fff;
  }

  header.header-mobile #mainmenu li a.menu-item:after {
    display: none;
  }

  header.header-mobile #mainmenu li:last-child {
    margin-bottom: 30px;
  }

  header.header-mobile #mainmenu li ul {
    border-top: none;
    top: auto;
  }

  header.header-mobile #mainmenu li ul a.menu-item {
    width: 100%;
    background: none;
    border: none;
  }

  header.header-mobile #mainmenu li ul li {
    border: none;
    padding-left: 40px;
  }

  header #mainmenu li ul li:first-child {
    border-top: solid 1px var(--bg-dark-3);
  }

  .dark-scheme header #mainmenu li ul li:first-child {
    border-top-color: rgba(255, 255, 255, .1);
  }

  header.header-mobile #mainmenu li ul li a.menu-item {
    display: block;
    border-bottom: solid 1px var(--bg-dark-3);
  }

  header.header-mobile #mainmenu li ul li:last-child {
    border-bottom: none;
    margin: 0;
  }

  header.header-mobile #mainmenu li ul li:last-child a.menu-item {
    border-bottom: none;
  }

  header.header-mobile #mainmenu li a {
    color: #ffffff;
  }

  .dark-scheme header.header-mobile #mainmenu li a.menu-item {
    color: #ffffff;
  }
  
  header.header-mobile #mainmenu li a.menu-item:hover {
    color: var(--primary-color);
  }

  .dark-scheme header.header-mobile #mainmenu li a.menu-item:hover {
    color: #fff;
  }

  header.header-mobile #mainmenu li ul {
    height: 0;
    overflow: hidden;
    position: relative;
    left: 0;
  }

  header.header-mobile #mainmenu li ul li ul {
    margin-bottom: 10px;
  }

  header.header-mobile #mainmenu>li>span {
    width: 36px;
    height: 36px;
    background: url(../images/ui/arrow-down-light.png) center no-repeat;
    position: absolute;
    right: 0;
    margin-top: 10px;
    z-index: 1000;
    cursor: poInter;
  }

  .dark-scheme header.header-mobile #mainmenu>li>span {
    background: url(../images/ui/arrow-down-light.png) center no-repeat;
  }

  header.header-mobile #mainmenu li span.active {
    background: url(../images/ui/arrow-up-light.png) center no-repeat;
  }

  .dark-scheme header.header-mobile #mainmenu li span.active {
    background: url(../images/ui/arrow-up-light.png) center no-repeat;
  }

  header.header-mobile #mainmenu li>ul>li>span {
    width: 36px;
    height: 36px;
    background: url(../images/ui/arrow-down.png) center no-repeat;
    position: absolute;
    right: 0;
    margin-top: -45px;
    z-index: 1000;
    cursor: poInter;
  }

  header.header-mobile #mainmenu>li>ul>li>span.active {
    background: url(../images/ui/arrow-up.png) center no-repeat;
  }

  header.header-mobile #mainmenu li ul li a.menu-item {
    border-bottom: solid 1px rgba(255, 255, 255, .2);
  }

  .dark-scheme header.header-mobile #mainmenu li ul li a.menu-item {
    border-bottom-color: rgba(255, 255, 255, .1);
  }

  header.header-mobile #mainmenu li ul li:last-child>a.menu-item {
    border-bottom: none;
  }

  header.header-light.header-mobile #mainmenu>li>span,
  header.header-light.header-mobile #mainmenu li>ul>li>span {
    background: url(../images/ui/arrow-down.png) center no-repeat;
  }

  header.header-light.header-mobile #mainmenu>li>span.active,
  header.header-light.header-mobile #mainmenu li>ul>li>span.active {
    background: url(../images/ui/arrow-up.png) center no-repeat;
  }

  #mainmenu li ul.mega {
    position: fixed;
    left: 0;
    width: 100%;
    margin-top: 30px;
    display: block;
  }

  #mainmenu li ul.mega>li {
    width: 100%;
  }

  #mainmenu li ul.mega>li ul {
    position: static;
    visibility: visible;
    opacity: 1;
    left: 0;
    float: none;
    width: 100%;
  }

  #mainmenu ul.mega>li ul li {
    margin: 0;
    padding: 0;
  }

  #mainmenu li ul.mega .menu-content {
    background: #202020;
    padding: 30px;
  }

  /* header light */
  .header-light #mainmenu ul li a.menu-item,
  .header-light #mainmenu ul li a.menu-item:hover {
    color: #333;
  }

  .de_tab.tab_methods .de_nav {
    text-align: center;
  }

  .de_tab.tab_methods .de_nav li {
    margin: 5px;
    padding: 0;
  }

  .de_tab.tab_methods .de_nav li span {
    margin: 0;
    display: inline-block;
  }

  .de_tab.tab_methods .de_nav li .v-border {
    border: none;
  }

  .hidden-phone {
    display: none;
  }

  .sm-hide {
    display: none;
  }

  .p-sm-relative {
    position: relative;
  }

  .d-btn-close {
    position: relative;
  }

  .de_tab.tab_style_4 .de_nav li {
    min-width: 100px;
  }

  #mainmenu a.menu-item span {
    display: none;
  }

  .de_tab.tab_methods.style-2 .de_nav li.active .arrow {
    margin-top: -40px;
    margin-bottom: 40px;

  }

  .table-custom-1 {
    position: relative;
    height: auto;
  }

  .timeline>li>.timeline-panel {
    width: 100%;
  }

  .timeline-badge {
    display: none;
  }

  .timeline:before {
    background: none;
  }

  .tp-caption {
    padding-left: 40px;
    padding-right: 40px;
  }

  .subfooter .de-flex,
  .subfooter .de-flex-col {
    display: block;
  }

  .subfooter .social-icons {
    margin-top: 20px;
  }

  .subfooter span.copy {
    margin-top: 20px;
    display: block;
  }

  .dropdown {
    display: inline-block;
    margin-bottom: 10px;
  }

  .sm-img-fluid{
    width: 100%;
  }

  section{
    padding: 60px 0;
  }

  #subheader h1:not(.cam-style){
    margin: 0;
    padding: 0;
  }

  #subheader{
    padding: 40px 0 80px 0 !important;
  }

  #testimonial-carousel .de_testi{
    margin:0 20px;
  }

  .crumb{
    margin-bottom: 0;
  }

  section.bottom-rounded{
    overflow: hidden;
    border-radius:0 0 0% 0%;
    -moz-border-radius:0 0 0% 0%;
    -webkit-border-radius:0 0 0% 0%;
    padding-bottom: 60px;
  }

  /* title tablet */

  h1,
  h1.ultra-big,
  h1.ultra-big .text-line,
  h1.ultra-big-2,
  .title-text,
  h1.slider-title{
    font-size: 60px;
  }  

  h1.slider-title,
  h2.slider-title{
    margin-bottom: 0;
  }

  h2{
    font-size: 40px;
  }

  .arrow-divider > [class*=col-]:nth-child(n+2):after{
    display: none;
  }

  .pos-sm-relative{
    position: relative !important;
  }

  /* padding top (px) */
  .pt-sm-10{padding-top:10px;}
  .pt-sm-20{padding-top:20px;}
  .pt-sm-30{padding-top:30px;}
  .pt-sm-40{padding-top:40px;}
  .pt-sm-50{padding-top:50px;}
  .pt-sm-60{padding-top:60px;}
  .pt-sm-70{padding-top:70px;}
  .pt-sm-80{padding-top:80px;}
  .pt-sm-90{padding-top:90px;}
  .pt-sm-100{padding-top:10px;}

  /* padding top (px) */
  .pb-sm-10{padding-bottom:10px;}
  .pb-sm-20{padding-bottom:20px;}
  .pb-sm-30{padding-bottom:30px;}
  .pb-sm-40{padding-bottom:40px;}
  .pb-sm-50{padding-bottom:50px;}
  .pb-sm-60{padding-bottom:60px;}
  .pb-sm-70{padding-bottom:70px;}
  .pb-sm-80{padding-bottom:80px;}
  .pb-sm-90{padding-bottom:90px;}
  .pb-sm-100{padding-bottom:10px;}

  /* margin top (px) */
  .mt-sm-10{margin-top:10px;}
  .mt-sm-20{margin-top:20px;}
  .mt-sm-30{margin-top:30px;}
  .mt-sm-40{margin-top:40px;}
  .mt-sm-50{margin-top:50px;}
  .mt-sm-60{margin-top:60px;}
  .mt-sm-70{margin-top:70px;}
  .mt-sm-80{margin-top:80px;}
  .mt-sm-90{margin-top:90px;}
  .mt-sm-100{margin-top:10px;}

  /* margin top (px) */
  .mb-sm-10{margin-bottom:10px;}
  .mb-sm-20{margin-bottom:20px;}
  .mb-sm-30{margin-bottom:30px;}
  .mb-sm-40{margin-bottom:40px;}
  .mb-sm-50{margin-bottom:50px;}
  .mb-sm-60{margin-bottom:60px;}
  .mb-sm-70{margin-bottom:70px;}
  .mb-sm-80{margin-bottom:80px;}
  .mb-sm-90{margin-bottom:90px;}
  .mb-sm-100{margin-bottom:10px;}


  .fs-sm-1vw{font-size: 1vw}
  .fs-sm-2vw{font-size: 2vw}
  .fs-sm-3vw{font-size: 3vw}
  .fs-sm-4vw{font-size: 4vw}
  .fs-sm-5vw{font-size: 5vw}
  .fs-sm-6vw{font-size: 6vw}
  .fs-sm-7vw{font-size: 7vw}
  .fs-sm-8vw{font-size: 8vw}
  .fs-sm-9vw{font-size: 9vw}
  .fs-sm-10vw{font-size: 10vw}
  .fs-sm-11vw{font-size: 11vw}
  .fs-sm-12vw{font-size: 12vw}
  .fs-sm-13vw{font-size: 13vw}
  .fs-sm-14vw{font-size: 14vw}
  .fs-sm-15vw{font-size: 15vw}

  #content{
    margin-top: 50px;
  }

  #btn-extra{
    display: none;
  }

  .sm-pt-0{
    padding-top: 0;
  }

  .de-quick-search{
    display: none;
  }

}

/* -------------------------------------------------- 
    custom for 320px & 480px 
    /* -------------------------------------------------- */

@media only screen and (max-width: 767px) {

   header .logo-main {
    display: none;
  }

  header .logo-init {
    display: none;
  }

  header .logo-mobile {
    display: block;
  }

  header .btn-main{
    display: none;
  }

  #topbar{
    display: none;
  }

  .menu_side_area {
    margin-left: 20px;
    width: 100%;
    position: relative;
    right: 0;
    text-align: right;
    z-index: 0;
    display: flex;
  }

  header .container {
    min-width: 100%;
  }

  .container {
    padding-left: 20px;
    padding-right: 20px;
  }

  #filters li.pull-right {
    float: none;
  }

  .text-item {
    font-size: 48px;
    line-height: 48px;
  }

  .text-item i {
    font-size: 36px;
  }

  .de_tab.tab_style_4 .de_nav {
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
  }

  .de_tab.tab_style_4 .de_nav li {
    min-width: 33.3%;
    width: 33.3%;
    border: none;
  }

  .de_tab.tab_style_4 .de_nav li:first-child,
  .de_tab.tab_style_4 .de_nav li:last-child {
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
  }
  
  .xs-hide {
    display: none;
  }

  header a.btn-main{
    display: none !important;
  }

  .scrollbar-v,
  .float-text{
    display: none;
  }

  .de-spot span{
    display: none;
  }

  .de-spot .de-circle-1,
  .de-spot .de-circle-2{
    transform: scale(.25);
  }

  section{
    padding: 40px 0;
  }

  #subheader{
    padding-top: 100px;
  }

  /* title phone */

  h1,
  h1.ultra-big,
  h1.ultra-big .text-line,
  h1.ultra-big-2,
  .title-text,
  h1.slider-title,
  h2.slider-title{
    font-size: 48px;
  }

  h2{
    font-size: 36px;
  }

  .mt-xs-0{
    margin-top: 0;
  }

  /* padding top (px) */
  .pt-xs-10{padding-top:10px;}
  .pt-xs-20{padding-top:20px;}
  .pt-xs-30{padding-top:30px;}
  .pt-xs-40{padding-top:40px;}
  .pt-xs-50{padding-top:50px;}
  .pt-xs-60{padding-top:60px;}
  .pt-xs-70{padding-top:70px;}
  .pt-xs-80{padding-top:80px;}
  .pt-xs-90{padding-top:90px;}
  .pt-xs-100{padding-top:10px;}

  /* padding top (px) */
  .pb-xs-10{padding-bottom:10px;}
  .pb-xs-20{padding-bottom:20px;}
  .pb-xs-30{padding-bottom:30px;}
  .pb-xs-40{padding-bottom:40px;}
  .pb-xs-50{padding-bottom:50px;}
  .pb-xs-60{padding-bottom:60px;}
  .pb-xs-70{padding-bottom:70px;}
  .pb-xs-80{padding-bottom:80px;}
  .pb-xs-90{padding-bottom:90px;}
  .pb-xs-100{padding-bottom:10px;}

  /* margin top (px) */
  .mt-xs-10{margin-top:10px;}
  .mt-xs-20{margin-top:20px;}
  .mt-xs-30{margin-top:30px;}
  .mt-xs-40{margin-top:40px;}
  .mt-xs-50{margin-top:50px;}
  .mt-xs-60{margin-top:60px;}
  .mt-xs-70{margin-top:70px;}
  .mt-xs-80{margin-top:80px;}
  .mt-xs-90{margin-top:90px;}
  .mt-xs-100{margin-top:10px;}

  /* margin top (px) */
  .mb-xs-10{margin-bottom:10px;}
  .mb-xs-20{margin-bottom:20px;}
  .mb-xs-30{margin-bottom:30px;}
  .mb-xs-40{margin-bottom:40px;}
  .mb-xs-50{margin-bottom:50px;}
  .mb-xs-60{margin-bottom:60px;}
  .mb-xs-70{margin-bottom:70px;}
  .mb-xs-80{margin-bottom:80px;}
  .mb-xs-90{margin-bottom:90px;}
  .mb-xs-100{margin-bottom:10px;}

  .ss-wrap{
    position: relative;
    width: 200%;
    margin-left: -50%;
  }

  .fs-xs-1vw{font-size: 1vw}
  .fs-xs-2vw{font-size: 2vw}
  .fs-xs-3vw{font-size: 3vw}
  .fs-xs-4vw{font-size: 4vw}
  .fs-xs-5vw{font-size: 5vw}
  .fs-xs-6vw{font-size: 6vw}
  .fs-xs-7vw{font-size: 7vw}
  .fs-xs-8vw{font-size: 8vw}
  .fs-xs-9vw{font-size: 9vw}
  .fs-xs-10vw{font-size: 10vw}
  .fs-xs-11vw{font-size: 11vw}
  .fs-xs-12vw{font-size: 12vw}
  .fs-xs-13vw{font-size: 13vw}
  .fs-xs-14vw{font-size: 14vw}
  .fs-xs-15vw{font-size: 15vw}

}

/* -------------------------------------------------- 
    design for 480px
    /* -------------------------------------------------- */

@media only screen and (min-width: 480px) and (max-width: 767px) {
  .container {
    padding-left: 30px;
    padding-right: 30px;
  }

  .timeline-panel {
    margin-bottom: 30px;
  }

  .timeline-pic {
    height: 300px;
  }
}

/* -------------------------------------------------- 
    design for 320px
    /* -------------------------------------------------- */

@media only screen and (max-width: 480px) {

  .text-item {
    font-size: 32px;
    line-height: 32px;
  }

  .text-item i {
    font-size: 24px;
  }

  h4.s2 span {
    display: block;
    margin: 0;
    padding: 0;
  }

  .h-phone {
    display: none;
  }

  header a.btn-main i {
    display: inline-block;
  }

}

@media only screen and (max-width: 360px) {
  #topbar {
    display: none;
  }
}

/* section control */
@media only screen and (min-device-width: 768) and (max-device-width: 1024) and (orientation: portrait) {
  section {
    -webkit-background-size: auto 100%;
    -moz-background-size: auto 100%;
    -o-background-size: auto 100%;
    background-size: auto 100%;
    background-attachment: scroll;
  }
}

@media only screen and (max-width: 992px) {
  #subheader {
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    background-attachment: scroll;
  }

  section {
    -webkit-background-size: auto 100%;
    -moz-background-size: auto 100%;
    -o-background-size: auto 100%;
    background-size: auto 100%;
    background-attachment: scroll;
  }
}

/* new added */
.half-fluid .left-half,
.half-fluid .right-half {
  left: 50%;
  margin-left: -50vw;
  max-width: 100vw;
  position: relative;
  right: 50%;
  width: 100vw;
  padding: 0px;
}
.half-fluid .image {
  background-size: cover;
  background-position: center;
  min-height: 400px;
  height: 100%;
}

@media (min-width: 992px) {
  .half-fluid .left-half {
    padding-left: 0 !important;
    position: absolute;
    left: 0;
    right: 50%;
    margin-left: auto;
    width: auto;
  }
  .half-fluid .right-half {
    padding-right: 0 !important;
    position: absolute;
    right: 0;
    left: 50%;
    margin-left: auto;
    width: auto;
  }
}
